// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

contract FaucetRegistry is Ownable, ReentrancyGuard {
    using SafeERC20 for IERC20;

    struct FaucetInfo {
        address tokenAddress;
        uint256 distributionAmount;
        uint256 totalDistributions;
        uint256 remainingDistributions;
        address registrant;
        bool isActive;
        uint256 registrationTime;
    }

    struct ClaimRecord {
        bool hasClaimed;
        uint256 claimTime;
    }

    bool public registrationEnabled = true;
    uint256 public totalFaucets;
    
    mapping(address => FaucetInfo) public faucets;
    mapping(address => bool) public blacklistedFaucets;
    mapping(address => mapping(address => ClaimRecord)) public claimRecords; // user => token => claim record
    
    address[] public faucetTokens;

    event FaucetRegistered(
        address indexed tokenAddress,
        address indexed registrant,
        uint256 distributionAmount,
        uint256 totalDistributions
    );
    
    event TokensDistributed(
        address indexed tokenAddress,
        address indexed recipient,
        uint256 amount
    );
    
    event ContributionMade(
        address indexed tokenAddress,
        address indexed contributor,
        uint256 additionalDistributions
    );
    
    event FaucetBlacklisted(address indexed tokenAddress, bool blacklisted);
    event RegistrationToggled(bool enabled);

    modifier onlyWhenRegistrationEnabled() {
        require(registrationEnabled, "Registration is disabled");
        _;
    }

    modifier onlyActiveFaucet(address tokenAddress) {
        require(faucets[tokenAddress].isActive, "Faucet not active");
        require(!blacklistedFaucets[tokenAddress], "Faucet is blacklisted");
        _;
    }

    constructor(address initialOwner) Ownable(initialOwner) {}

    function registerFaucet(
        address tokenAddress,
        uint256 distributionAmount,
        uint256 totalDistributions
    ) external onlyWhenRegistrationEnabled nonReentrant {
        require(tokenAddress != address(0), "Invalid token address");
        require(distributionAmount > 0, "Distribution amount must be greater than 0");
        require(totalDistributions > 0, "Total distributions must be greater than 0");
        require(!faucets[tokenAddress].isActive, "Faucet already registered");

        uint256 totalTokensNeeded = distributionAmount * totalDistributions;
        
        IERC20(tokenAddress).safeTransferFrom(
            msg.sender,
            address(this),
            totalTokensNeeded
        );

        faucets[tokenAddress] = FaucetInfo({
            tokenAddress: tokenAddress,
            distributionAmount: distributionAmount,
            totalDistributions: totalDistributions,
            remainingDistributions: totalDistributions,
            registrant: msg.sender,
            isActive: true,
            registrationTime: block.timestamp
        });

        faucetTokens.push(tokenAddress);
        totalFaucets++;

        emit FaucetRegistered(tokenAddress, msg.sender, distributionAmount, totalDistributions);
    }

    function claimTokens(address tokenAddress) external onlyActiveFaucet(tokenAddress) nonReentrant {
        require(!claimRecords[msg.sender][tokenAddress].hasClaimed, "Already claimed");
        require(faucets[tokenAddress].remainingDistributions > 0, "No more distributions available");

        claimRecords[msg.sender][tokenAddress] = ClaimRecord({
            hasClaimed: true,
            claimTime: block.timestamp
        });

        faucets[tokenAddress].remainingDistributions--;

        IERC20(tokenAddress).safeTransfer(msg.sender, faucets[tokenAddress].distributionAmount);

        emit TokensDistributed(tokenAddress, msg.sender, faucets[tokenAddress].distributionAmount);
    }

    function distributeTokens(address tokenAddress, address recipient) 
        external 
        onlyOwner 
        onlyActiveFaucet(tokenAddress) 
        nonReentrant 
    {
        require(recipient != address(0), "Invalid recipient");
        require(faucets[tokenAddress].remainingDistributions > 0, "No more distributions available");

        faucets[tokenAddress].remainingDistributions--;

        IERC20(tokenAddress).safeTransfer(recipient, faucets[tokenAddress].distributionAmount);

        emit TokensDistributed(tokenAddress, recipient, faucets[tokenAddress].distributionAmount);
    }

    function contributeToFaucet(address tokenAddress, uint256 additionalDistributions) 
        external 
        onlyActiveFaucet(tokenAddress) 
        nonReentrant 
    {
        require(additionalDistributions > 0, "Additional distributions must be greater than 0");

        uint256 tokensNeeded = faucets[tokenAddress].distributionAmount * additionalDistributions;
        
        IERC20(tokenAddress).safeTransferFrom(
            msg.sender,
            address(this),
            tokensNeeded
        );

        faucets[tokenAddress].totalDistributions += additionalDistributions;
        faucets[tokenAddress].remainingDistributions += additionalDistributions;

        emit ContributionMade(tokenAddress, msg.sender, additionalDistributions);
    }

    function getFaucetInfo(address tokenAddress) external view returns (FaucetInfo memory) {
        return faucets[tokenAddress];
    }

    function getFaucetsPaginated(uint256 offset, uint256 limit) 
        external 
        view 
        returns (FaucetInfo[] memory faucetInfos, uint256 total) 
    {
        total = totalFaucets;
        
        if (offset >= total) {
            return (new FaucetInfo[](0), total);
        }
        
        uint256 end = offset + limit;
        if (end > total) {
            end = total;
        }
        
        uint256 length = end - offset;
        faucetInfos = new FaucetInfo[](length);
        
        for (uint256 i = 0; i < length; i++) {
            address tokenAddress = faucetTokens[offset + i];
            faucetInfos[i] = faucets[tokenAddress];
        }
    }

    function getActiveFaucetsPaginated(uint256 offset, uint256 limit) 
        external 
        view 
        returns (FaucetInfo[] memory faucetInfos, uint256 total) 
    {
        address[] memory activeFaucets = new address[](totalFaucets);
        uint256 activeCount = 0;
        
        for (uint256 i = 0; i < totalFaucets; i++) {
            address tokenAddress = faucetTokens[i];
            if (faucets[tokenAddress].isActive && !blacklistedFaucets[tokenAddress]) {
                activeFaucets[activeCount] = tokenAddress;
                activeCount++;
            }
        }
        
        total = activeCount;
        
        if (offset >= total) {
            return (new FaucetInfo[](0), total);
        }
        
        uint256 end = offset + limit;
        if (end > total) {
            end = total;
        }
        
        uint256 length = end - offset;
        faucetInfos = new FaucetInfo[](length);
        
        for (uint256 i = 0; i < length; i++) {
            address tokenAddress = activeFaucets[offset + i];
            faucetInfos[i] = faucets[tokenAddress];
        }
    }

    function hasUserClaimed(address user, address tokenAddress) external view returns (bool, uint256) {
        ClaimRecord memory record = claimRecords[user][tokenAddress];
        return (record.hasClaimed, record.claimTime);
    }

    function isFaucetRegistered(address tokenAddress) external view returns (bool) {
        return faucets[tokenAddress].isActive;
    }

    function setFaucetBlacklist(address tokenAddress, bool blacklisted) external onlyOwner {
        require(faucets[tokenAddress].isActive, "Faucet not registered");
        blacklistedFaucets[tokenAddress] = blacklisted;
        emit FaucetBlacklisted(tokenAddress, blacklisted);
    }

    function toggleRegistration(bool enabled) external onlyOwner {
        registrationEnabled = enabled;
        emit RegistrationToggled(enabled);
    }

    function emergencyWithdraw(address tokenAddress, uint256 amount) external onlyOwner {
        IERC20(tokenAddress).safeTransfer(owner(), amount);
    }
}
