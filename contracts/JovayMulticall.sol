// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/utils/Multicall.sol";

/**
 * @title JovayMulticall
 * @dev A wrapper contract that provides multicall functionality for efficient batch operations
 * This contract allows multiple function calls to be batched into a single transaction,
 * reducing gas costs and improving user experience.
 * 
 * Based on OpenZeppelin's Multicall utility (v5.x)
 * Documentation: https://docs.openzeppelin.com/contracts/5.x/api/utils#Multicall
 */
contract JovayMulticall is Multicall {
    
    // Events
    event MulticallExecuted(address indexed caller, uint256 callsCount);
    event ContractCall(address indexed target, bytes data, bool success);
    
    constructor() {}
    
    /**
     * @dev Executes multiple calls in a single transaction to external contracts
     * @param targets Array of contract addresses to call
     * @param data Array of encoded function call data
     * @return results Array of return data from each call
     * 
     * Note: This function allows calling external contracts. Use with caution.
     */
    function multiCall(
        address[] calldata targets,
        bytes[] calldata data
    ) external returns (bytes[] memory results) {
        require(targets.length == data.length, "Arrays length mismatch");
        require(targets.length > 0, "No calls provided");
        
        results = new bytes[](targets.length);
        
        for (uint256 i = 0; i < targets.length; i++) {
            (bool success, bytes memory result) = targets[i].call(data[i]);
            
            if (!success) {
                // Extract revert reason if available
                if (result.length > 0) {
                    assembly {
                        let returndata_size := mload(result)
                        revert(add(32, result), returndata_size)
                    }
                } else {
                    revert("Multicall: call failed");
                }
            }
            
            results[i] = result;
            emit ContractCall(targets[i], data[i], success);
        }
        
        emit MulticallExecuted(msg.sender, targets.length);
        return results;
    }
    
    /**
     * @dev Executes multiple view calls in a single transaction (read-only operations)
     * @param targets Array of contract addresses to call
     * @param data Array of encoded function call data
     * @return results Array of return data from each call
     * 
     * This function is optimized for read operations and doesn't emit events
     * to save gas for view calls.
     */
    function multiStaticCall(
        address[] calldata targets,
        bytes[] calldata data
    ) external view returns (bytes[] memory results) {
        require(targets.length == data.length, "Arrays length mismatch");
        require(targets.length > 0, "No calls provided");
        
        results = new bytes[](targets.length);
        
        for (uint256 i = 0; i < targets.length; i++) {
            (bool success, bytes memory result) = targets[i].staticcall(data[i]);
            
            if (!success) {
                // Extract revert reason if available
                if (result.length > 0) {
                    assembly {
                        let returndata_size := mload(result)
                        revert(add(32, result), returndata_size)
                    }
                } else {
                    revert("Multicall: static call failed");
                }
            }
            
            results[i] = result;
        }
        
        return results;
    }
    
    /**
     * @dev Executes multiple calls with individual success/failure handling
     * @param targets Array of contract addresses to call
     * @param data Array of encoded function call data
     * @param requireSuccess Array indicating whether each call must succeed
     * @return results Array of return data from each call
     * @return successes Array indicating whether each call succeeded
     * 
     * This function allows some calls to fail without reverting the entire transaction
     */
    function multiCallWithResults(
        address[] calldata targets,
        bytes[] calldata data,
        bool[] calldata requireSuccess
    ) external returns (bytes[] memory results, bool[] memory successes) {
        require(
            targets.length == data.length && data.length == requireSuccess.length,
            "Arrays length mismatch"
        );
        require(targets.length > 0, "No calls provided");
        
        results = new bytes[](targets.length);
        successes = new bool[](targets.length);
        
        for (uint256 i = 0; i < targets.length; i++) {
            (bool success, bytes memory result) = targets[i].call(data[i]);
            
            if (!success && requireSuccess[i]) {
                // Extract revert reason if available
                if (result.length > 0) {
                    assembly {
                        let returndata_size := mload(result)
                        revert(add(32, result), returndata_size)
                    }
                } else {
                    revert("Multicall: required call failed");
                }
            }
            
            results[i] = result;
            successes[i] = success;
            emit ContractCall(targets[i], data[i], success);
        }
        
        emit MulticallExecuted(msg.sender, targets.length);
        return (results, successes);
    }
    
    /**
     * @dev Reject direct ETH transfers to prevent misuse
     * This contract is designed for batch operations, not for holding funds
     */
    receive() external payable {
        revert("JovayMulticall: Direct ETH transfers not allowed");
    }
    
    /**
     * @dev Reject fallback calls with data to prevent misuse
     */
    fallback() external payable {
        revert("JovayMulticall: Fallback calls not allowed");
    }
}
