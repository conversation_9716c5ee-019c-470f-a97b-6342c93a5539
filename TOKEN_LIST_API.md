# Token List API 文档

## 概述

新增了 `/api/tokenlist/testnet` 接口，用于为 Jovay 链上的 Uniswap 提供 ERC20 token 列表。该接口返回符合 Uniswap Token List 标准的 JSON 格式数据。

## 接口详情

### 端点
```
GET /api/tokenlist/testnet
```

### 功能特性
- ✅ **CORS 支持**: 完全支持跨域访问
- ✅ **缓存机制**: 3分钟缓存，减少合约查询频率
- ✅ **合约集成**: 自动从 FaucetRegistry 合约获取活跃 faucet tokens
- ✅ **白名单支持**: 支持预定义 token 白名单（当前为空）
- ✅ **去重处理**: 自动合并和去重 token 列表
- ✅ **错误处理**: 完善的错误处理和降级机制

### 响应格式

符合 [Uniswap Token List 标准](https://github.com/Uniswap/token-lists) 的 JSON 格式：

```json
{
  "name": "Jovay Testnet Token List",
  "timestamp": "2025-08-01T16:59:19.460Z",
  "version": {
    "major": 1,
    "minor": 0,
    "patch": 0
  },
  "tags": {},
  "logoURI": "",
  "keywords": ["jovay", "testnet", "faucet"],
  "tokens": [
    {
      "chainId": 2019775,
      "address": "0xEF6A7ae0efbe9b5AD9B720B551Feca66893a8aD7",
      "decimals": 18,
      "symbol": "MTK1",
      "name": "MyToken1",
      "logoURI": ""
    }
  ]
}
```

### Token 对象字段

每个 token 对象包含以下必需字段：

| 字段 | 类型 | 描述 |
|------|------|------|
| `chainId` | number | 链 ID (Jovay Testnet: 2019775) |
| `address` | string | Token 合约地址 |
| `decimals` | number | Token 精度 |
| `symbol` | string | Token 符号 |
| `name` | string | Token 名称 |
| `logoURI` | string | Token 图标 URI (当前为空) |

## 实现细节

### 数据源
1. **预定义白名单**: 在 `src/tokenList.js` 中配置的静态 token 列表（当前为空）
2. **FaucetRegistry 合约**: 通过 `getActiveFaucetsPaginated(0, 100)` 获取活跃的 faucet tokens

### 缓存机制
- **缓存时长**: 3分钟
- **缓存键**: `'activeFaucets'`
- **降级策略**: 如果合约查询失败，返回过期缓存数据（如果有）

### 合约配置
- **FaucetRegistry 地址**: `0x6Eb9D235f8Ca31Ca63Fb99319F77050B63315770`
- **RPC 端点**: `https://api.zan.top/public/jovay-testnet`
- **查询限制**: 每次最多获取 100 个 tokens

## 使用示例

### JavaScript/TypeScript
```javascript
// 获取 token 列表
const response = await fetch('https://your-domain.com/api/tokenlist/testnet');
const tokenList = await response.json();

console.log('Available tokens:', tokenList.tokens.length);
tokenList.tokens.forEach(token => {
  console.log(`${token.symbol}: ${token.address}`);
});
```

### cURL
```bash
curl -X GET "https://your-domain.com/api/tokenlist/testnet" \
  -H "Accept: application/json"
```

## 错误处理

### 成功响应
- **状态码**: 200
- **Content-Type**: `application/json`

### 错误响应
- **状态码**: 500
- **响应格式**:
```json
{
  "error": "Failed to generate token list: [错误详情]"
}
```

### 降级机制
如果合约查询失败，接口会：
1. 尝试返回缓存的数据（即使过期）
2. 如果没有缓存，返回仅包含预定义 tokens 的列表
3. 如果完全失败，返回空的 token 列表

## 配置选项

### 预定义 Token 白名单
在 `src/tokenList.js` 中的 `predefinedTokens` 数组中添加：

```javascript
const predefinedTokens = [
  {
    chainId: 2019775,
    address: '0x...',
    decimals: 18,
    symbol: 'TOKEN',
    name: 'Token Name',
    logoURI: 'https://...'
  }
];
```

### 缓存配置
在 `src/tokenList.js` 中修改 `CACHE_DURATION` 常量：

```javascript
const CACHE_DURATION = 3 * 60 * 1000; // 3分钟
```

## 测试验证

接口已通过以下测试：
- ✅ 合约数据获取正常
- ✅ Token 信息解析正确
- ✅ 缓存机制工作正常
- ✅ CORS 头设置正确
- ✅ Uniswap Token List 格式验证通过
- ✅ 错误处理机制完善

## 性能优化

1. **缓存策略**: 3分钟缓存减少合约查询
2. **并发查询**: 使用 `Promise.all` 并发获取 token 信息
3. **错误容错**: 单个 token 查询失败不影响整体结果
4. **内存管理**: 自动清理过期缓存条目

## 维护说明

- 定期检查 FaucetRegistry 合约地址是否正确
- 根据需要调整缓存时长
- 监控接口响应时间和错误率
- 根据实际使用情况调整查询限制（当前为100）
