#!/bin/bash

# ERC20 Faucet DApp Deployment Script

echo "🚀 ERC20 Faucet DApp Deployment"
echo "================================="

# Check if required environment variables are set
if [ -z "$DEPLOYER_PRIVATE_KEY" ]; then
    echo "❌ Error: DEPLOYER_PRIVATE_KEY environment variable is not set"
    echo "Please set it with: export DEPLOYER_PRIVATE_KEY=your_private_key"
    exit 1
fi

if [ -z "$JOVAY_TESTNET_RPC_URL" ]; then
    echo "⚠️  Warning: JOVAY_TESTNET_RPC_URL not set, using default"
    export JOVAY_TESTNET_RPC_URL="https://api.zan.top/public/jovay-testnet"
fi

echo "✅ Environment variables configured"
echo ""

# Step 1: Install dependencies
echo "📦 Installing dependencies..."
npm install
cd frontend && npm install && cd ..
echo "✅ Dependencies installed"
echo ""

# Step 2: Compile smart contracts
echo "🔨 Compiling smart contracts..."
npx hardhat compile
if [ $? -ne 0 ]; then
    echo "❌ Contract compilation failed"
    exit 1
fi
echo "✅ Smart contracts compiled"
echo ""

# Step 3: Deploy FaucetRegistry contract
echo "🚀 Deploying FaucetRegistry contract to Jovay Testnet..."
DEPLOY_OUTPUT=$(npx hardhat run scripts/deploy-faucet.js --network jovay_testnet 2>&1)
if [ $? -ne 0 ]; then
    echo "❌ Contract deployment failed"
    echo "$DEPLOY_OUTPUT"
    exit 1
fi

# Extract contract address from deployment output
CONTRACT_ADDRESS=$(echo "$DEPLOY_OUTPUT" | grep "FaucetRegistry deployed to:" | awk '{print $4}')
if [ -z "$CONTRACT_ADDRESS" ]; then
    echo "❌ Could not extract contract address from deployment output"
    echo "$DEPLOY_OUTPUT"
    exit 1
fi

echo "✅ FaucetRegistry deployed to: $CONTRACT_ADDRESS"
echo ""

# Step 4: Update frontend configuration
echo "🔧 Updating frontend configuration..."
sed -i.bak "s/export const faucetRegistryAddress = '.*'/export const faucetRegistryAddress = '$CONTRACT_ADDRESS'/" frontend/src/config/constants.ts
echo "✅ Frontend configuration updated"
echo ""

# Step 5: Build frontend
echo "🏗️  Building frontend..."
cd frontend
npm run build
if [ $? -ne 0 ]; then
    echo "❌ Frontend build failed"
    exit 1
fi
cd ..
echo "✅ Frontend built successfully"
echo ""

# Step 6: Instructions for Cloudflare Pages deployment
echo "☁️  Cloudflare Pages Deployment Instructions"
echo "=============================================="
echo ""
echo "1. Set environment variables in Cloudflare Pages:"
echo "   FAUCET_REGISTRY_ADDRESS = $CONTRACT_ADDRESS"
echo ""
echo "2. Set the private key as a secret:"
echo "   wrangler secret put FAUCET_PRIVATE_KEY"
echo "   (Enter your admin private key when prompted)"
echo ""
echo "3. Deploy to Cloudflare Pages:"
echo "   wrangler deploy"
echo ""
echo "🎉 Deployment preparation complete!"
echo ""
echo "📋 Summary:"
echo "- Contract Address: $CONTRACT_ADDRESS"
echo "- Frontend built: frontend/dist/"
echo "- Ready for Cloudflare Pages deployment"
echo ""
echo "⚠️  Important: Make sure to set FAUCET_PRIVATE_KEY as a secret in Cloudflare"
echo "   The private key should be for the admin account that deployed the contract"