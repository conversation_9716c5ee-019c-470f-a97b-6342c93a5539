import { checkRateLimit, getRateLimitStatus, getRateLimitStoreSize } from './src/rateLimit.js';
import { getClientIP, createResponse, isValidAddress, createCorsResponse } from './src/utils.js';
import { claimTokens } from './src/blockchain.js';

export default {
  async fetch(request, env) {
    const url = new URL(request.url);

    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return createCorsResponse();
    }
    
    if (url.pathname === '/api/claim' && request.method === 'POST') {
      try {
        // Get client IP for rate limiting
        const clientIP = getClientIP(request);
        
        // Check rate limit
        const rateLimit = checkRateLimit(clientIP);
        if (!rateLimit.allowed) {
          return createResponse({
            success: false,
            error: `Rate limit exceeded. Please wait ${rateLimit.remainingTime} seconds before claiming again.`,
            remainingTime: rateLimit.remainingTime
          }, 429);
        }
        
        // Parse request body
        const body = await request.json();
        const { tokenAddress, userAddress } = body;
        
        if (!tokenAddress || !userAddress) {
          return createResponse({
            success: false,
            error: 'Missing required parameters: tokenAddress and userAddress'
          }, 400);
        }
        
        // Validate addresses
        if (!isValidAddress(tokenAddress) || !isValidAddress(userAddress)) {
          return createResponse({
            success: false,
            error: 'Invalid address format'
          }, 400);
        }

        // Execute token claim
        const result = await claimTokens(env, tokenAddress, userAddress);

        return createResponse({
          success: true,
          transactionHash: result.transactionHash,
          message: 'Tokens claimed successfully!',
          distributionAmount: result.distributionAmount
        });
        
      } catch (error) {
        console.error('Claim API error:', error);

        // Handle specific error cases
        if (error.message.includes('already claimed') ||
            error.message.includes('Already claimed')) {
          return createResponse({
            success: false,
            error: 'You have already claimed tokens from this faucet'
          }, 400);
        }

        if (error.message.includes('No more tokens available') ||
            error.message.includes('No more distributions available')) {
          return createResponse({
            success: false,
            error: 'No more tokens available in this faucet'
          }, 400);
        }

        if (error.message.includes('not active') ||
            error.message.includes('Faucet not active')) {
          return createResponse({
            success: false,
            error: 'This faucet is not active'
          }, 400);
        }

        if (error.message.includes('Server configuration error')) {
          return createResponse({
            success: false,
            error: error.message
          }, 500);
        }

        return createResponse({
          success: false,
          error: 'Internal server error: ' + (error.message || 'Unknown error')
        }, 500);
      }
    }
    
    // Health check endpoint
    if (url.pathname === '/api/health') {
      return createResponse({
        status: 'ok',
        timestamp: new Date().toISOString(),
        rateLimitEntries: getRateLimitStoreSize()
      });
    }

    // Rate limit status endpoint
    if (url.pathname === '/api/rate-limit' && request.method === 'GET') {
      const clientIP = getClientIP(request);
      const status = getRateLimitStatus(clientIP);
      return createResponse(status);
    }
    
    // Serve static assets for other requests
    return env.ASSETS.fetch(request);
  },
};
