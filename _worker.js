import { createPublicClient, createWalletClient, http } from 'viem';
import { define<PERSON><PERSON><PERSON> } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';

// Define Jovay Testnet chain
const jovayTestnet = defineChain({
  id: 2019775,
  name: 'Jovay Testnet',
  nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://api.zan.top/public/jovay-testnet'],
    },
  },
  blockExplorers: {
    default: { name: 'Jovay Testnet Explorer', url: 'https://sepolia-explorer.jovay.io/l2' },
  },
  testnet: true,
});

// FaucetRegistry ABI (key functions only)
const faucetRegistryAbi = [
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "tokenAddress",
        "type": "address"
      },
      {
        "internalType": "address",
        "name": "recipient",
        "type": "address"
      }
    ],
    "name": "distributeTokens",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "tokenAddress",
        "type": "address"
      }
    ],
    "name": "getFaucetInfo",
    "outputs": [
      {
        "components": [
          {
            "internalType": "address",
            "name": "tokenAddress",
            "type": "address"
          },
          {
            "internalType": "uint256",
            "name": "distributionAmount",
            "type": "uint256"
          },
          {
            "internalType": "uint256",
            "name": "totalDistributions",
            "type": "uint256"
          },
          {
            "internalType": "uint256",
            "name": "remainingDistributions",
            "type": "uint256"
          },
          {
            "internalType": "address",
            "name": "registrant",
            "type": "address"
          },
          {
            "internalType": "bool",
            "name": "isActive",
            "type": "bool"
          },
          {
            "internalType": "uint256",
            "name": "registrationTime",
            "type": "uint256"
          }
        ],
        "internalType": "struct FaucetRegistry.FaucetInfo",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "user",
        "type": "address"
      },
      {
        "internalType": "address",
        "name": "tokenAddress",
        "type": "address"
      }
    ],
    "name": "hasUserClaimed",
    "outputs": [
      {
        "internalType": "bool",
        "name": "",
        "type": "bool"
      },
      {
        "internalType": "uint256",
        "name": "",
        "type": "uint256"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

// In-memory rate limiting storage
const rateLimitStore = new Map();

// Rate limiting function
function checkRateLimit(ip) {
  const now = Date.now();
  const lastClaim = rateLimitStore.get(ip);
  
  if (lastClaim && (now - lastClaim) < 60000) { // 1 minute = 60000ms
    return {
      allowed: false,
      remainingTime: Math.ceil((60000 - (now - lastClaim)) / 1000)
    };
  }
  
  rateLimitStore.set(ip, now);
  
  // Clean up old entries (older than 1 minute)
  for (const [key, value] of rateLimitStore.entries()) {
    if (now - value > 60000) {
      rateLimitStore.delete(key);
    }
  }
  
  return { allowed: true, remainingTime: 0 };
}

// Get client IP address
function getClientIP(request) {
  return request.headers.get('CF-Connecting-IP') || 
         request.headers.get('X-Real-IP') || 
         request.headers.get('X-Forwarded-For')?.split(',')[0] || 
         '127.0.0.1';
}

// Create response with CORS headers
function createResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

export default {
  async fetch(request, env) {
    const url = new URL(request.url);
    
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });
    }
    
    if (url.pathname === '/api/claim' && request.method === 'POST') {
      try {
        // Get client IP for rate limiting
        const clientIP = getClientIP(request);
        
        // Check rate limit
        const rateLimit = checkRateLimit(clientIP);
        if (!rateLimit.allowed) {
          return createResponse({
            success: false,
            error: `Rate limit exceeded. Please wait ${rateLimit.remainingTime} seconds before claiming again.`,
            remainingTime: rateLimit.remainingTime
          }, 429);
        }
        
        // Parse request body
        const body = await request.json();
        const { tokenAddress, userAddress } = body;
        
        if (!tokenAddress || !userAddress) {
          return createResponse({
            success: false,
            error: 'Missing required parameters: tokenAddress and userAddress'
          }, 400);
        }
        
        // Validate addresses
        if (!/^0x[a-fA-F0-9]{40}$/.test(tokenAddress) || !/^0x[a-fA-F0-9]{40}$/.test(userAddress)) {
          return createResponse({
            success: false,
            error: 'Invalid address format'
          }, 400);
        }

        
        // Check environment variables
        if (!env.FAUCET_REGISTRY_ADDRESS) {
          return createResponse({
            success: false,
            error: 'Server configuration error: Missing faucet registry contract address'
          }, 500);
        }
        
        if (!env.FAUCET_PRIVATE_KEY) {
          return createResponse({
            success: false,
            error: 'Server configuration error: Missing faucet admin private key'
          }, 500);
        }
        
        // Create clients
        const publicClient = createPublicClient({
          chain: jovayTestnet,
          transport: http()
        });
        
        const account = privateKeyToAccount(env.FAUCET_PRIVATE_KEY);
        const walletClient = createWalletClient({
          account,
          chain: jovayTestnet,
          transport: http()
        });
        
        // Check if faucet exists and is active
        const faucetInfo = await publicClient.readContract({
          address: env.FAUCET_REGISTRY_ADDRESS,
          abi: faucetRegistryAbi,
          functionName: 'getFaucetInfo',
          args: [tokenAddress]
        });
        
        if (!faucetInfo.isActive) {
          return createResponse({
            success: false,
            error: 'This faucet is not active'
          }, 400);
        }
        
        if (faucetInfo.remainingDistributions === 0n) {
          return createResponse({
            success: false,
            error: 'No more tokens available in this faucet'
          }, 400);
        }
        
        // Check if user has already claimed
        const [hasClaimed] = await publicClient.readContract({
          address: env.FAUCET_REGISTRY_ADDRESS,
          abi: faucetRegistryAbi,
          functionName: 'hasUserClaimed',
          args: [userAddress, tokenAddress]
        });
        
        if (hasClaimed) {
          return createResponse({
            success: false,
            error: 'You have already claimed tokens from this faucet'
          }, 400);
        }
        
        // Execute the distribution transaction (admin distributes to user)
        const hash = await walletClient.writeContract({
          address: env.FAUCET_REGISTRY_ADDRESS,
          abi: faucetRegistryAbi,
          functionName: 'distributeTokens',
          args: [tokenAddress, userAddress]
        });
        
        // Wait for transaction confirmation
        const receipt = await publicClient.waitForTransactionReceipt({
          hash,
          confirmations: 1
        });
        
        if (receipt.status === 'success') {
          return createResponse({
            success: true,
            transactionHash: hash,
            message: 'Tokens claimed successfully!',
            distributionAmount: faucetInfo.distributionAmount.toString()
          });
        } else {
          return createResponse({
            success: false,
            error: 'Transaction failed',
            transactionHash: hash
          }, 500);
        }
        
      } catch (error) {
        console.error('Claim API error:', error);
        
        // Handle specific error cases
        if (error.message.includes('Already claimed')) {
          return createResponse({
            success: false,
            error: 'You have already claimed tokens from this faucet'
          }, 400);
        }
        
        if (error.message.includes('No more distributions available')) {
          return createResponse({
            success: false,
            error: 'No more tokens available in this faucet'
          }, 400);
        }
        
        if (error.message.includes('Faucet not active')) {
          return createResponse({
            success: false,
            error: 'This faucet is not active'
          }, 400);
        }
        
        return createResponse({
          success: false,
          error: 'Internal server error: ' + (error.message || 'Unknown error')
        }, 500);
      }
    }
    
    // Health check endpoint
    if (url.pathname === '/api/health') {
      return createResponse({
        status: 'ok',
        timestamp: new Date().toISOString(),
        rateLimitEntries: rateLimitStore.size
      });
    }
    
    // Rate limit status endpoint
    if (url.pathname === '/api/rate-limit' && request.method === 'GET') {
      const clientIP = getClientIP(request);
      const now = Date.now();
      const lastClaim = rateLimitStore.get(clientIP);
      
      if (lastClaim && (now - lastClaim) < 60000) {
        const remainingTime = Math.ceil((60000 - (now - lastClaim)) / 1000);
        return createResponse({
          canClaim: false,
          remainingTime
        });
      }
      
      return createResponse({
        canClaim: true,
        remainingTime: 0
      });
    }
    
    // Serve static assets for other requests
    return env.ASSETS.fetch(request);
  },
};
