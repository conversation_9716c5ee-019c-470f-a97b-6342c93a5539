<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import { useAccount, useConnect, useDisconnect, useChainId, useSwitchChain, useBalance } from '@wagmi/vue'
import { jovayTestnet } from './wagmi'
import { injected } from '@wagmi/vue/connectors'
import { computed } from 'vue'


const { address: walletAddress, isConnected } = useAccount()
const { connect } = useConnect()
const { disconnect } = useDisconnect()
const chainId = useChainId()
const { switchChain } = useSwitchChain()

const { data: balance } = useBalance({ address: walletAddress })

const isJovayTestnet = computed(() => chainId.value === jovayTestnet.id)

const switchToJovayTestnet = () => {
  switchChain({ chainId: jovayTestnet.id })
}

const connectWallet = async () => {
  // Ensure fresh connection by clearing any cached connection data
  if (typeof window !== 'undefined') {
    localStorage.removeItem('wagmi.connected')
    localStorage.removeItem('wagmi.wallet')
  }
  connect({ connector: injected() })
}

const disconnectWallet = () => {
  disconnect()
  // Clear any local storage or session data if needed
  if (typeof window !== 'undefined') {
    localStorage.removeItem('wagmi.connected')
    localStorage.removeItem('wagmi.wallet')
    // Disconnect OKX wallet if available
    const okxWallet = (window as any).okxWallet;
    if (typeof okxWallet !== 'undefined') {
      okxWallet.disconnect()
    }
    // Disconnect ethereum wallet if available
    if (window.ethereum) {
      window.ethereum.disconnect()
    }
  }
}

</script>

<template>
  <div class="app">
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo-nav">
            <RouterLink to="/" class="logo">
              <img src="@/assets/logo.png" alt="Logo" class="logo-image">
              <h1 class="logo-text">ERC20 Faucet</h1>
            </RouterLink>
            <nav class="header-nav">
              <RouterLink to="/faucets" class="nav-link">
                Faucets
              </RouterLink>
              <RouterLink to="/register" class="nav-link">
                Register
              </RouterLink>
              <RouterLink to="/admin" class="nav-link">
                Admin
              </RouterLink>
            </nav>
          </div>
          
          <div class="wallet-section">
            <button
              v-if="!isConnected"
              @click="connectWallet"
              class="btn btn-primary btn-rounded"
            >
              Connect Wallet
            </button>
            <div v-else class="wallet-info">
              <div v-if="isJovayTestnet" class="status-badge status-success">
                Jovay Testnet
              </div>
              <div v-else-if="isConnected && !isJovayTestnet" class="network-warning">
                <div class="status-badge status-danger">
                  Wrong Network
                </div>
                <button @click="switchToJovayTestnet" class="btn btn-primary btn-sm">
                  Switch to Jovay Testnet
                </button>
              </div>
              <div v-if="balance" class="balance-display">
                {{ parseFloat(balance.formatted).toFixed(4) }} {{ balance.symbol }}
              </div>
              <div class="address-display">
                {{ walletAddress }}
              </div>
              <button
                @click="disconnectWallet"
                class="btn btn-danger btn-sm btn-rounded"
                title="Disconnect Wallet"
              >
                Disconnect
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
    
    <main class="main-content">
      <div class="container">
        <RouterView />
      </div>
    </main>
    
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <p class="footer-description">
            A decentralized ERC20 faucet DApp on Jovay Testnet that allows token owners to register faucets, enables one-time claims per user, and supports community-driven token contributions.
          </p>
          <p class="footer-copyright">ERC20 Faucet DApp © {{ new Date().getFullYear() }}</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.logo-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-primary);
}

.logo-image {
  width: 2rem;
  height: 2rem;
  margin-right: 0.5rem;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.wallet-section {
  display: flex;
  align-items: center;
}

.wallet-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.network-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.balance-display {
  background: var(--bg-accent);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.address-display {
  background: var(--bg-accent);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.main-content {
  flex: 1;
  padding: 2rem 0;
  width: 100%;
}

.footer-content {
  text-align: center;
  padding: 2rem 0;
}

.footer-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.footer-copyright {
  font-size: 0.75rem;
  color: var(--text-light);
}

@media (max-width: 768px) {
  .logo-nav {
    gap: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .wallet-section {
    justify-content: center;
  }
  
  .wallet-info {
    justify-content: center;
    gap: 0.5rem;
  }
  
  .address-display {
    max-width: 150px;
  }
}
</style>