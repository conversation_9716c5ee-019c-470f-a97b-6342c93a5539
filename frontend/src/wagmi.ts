import { http, createConfig } from 'wagmi'
import { define<PERSON>hain } from 'viem'

import faucetRegistryContractAbi from './contracts/FaucetRegistry.abi.json';

export const faucetRegistryAbi = faucetRegistryContractAbi;

export const jovayTestnet = defineChain({
  id: 2019775,
  name: 'Jovay Testnet',
  nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://api.zan.top/public/jovay-testnet'],
    },
  },
  blockExplorers: {
    default: { name: 'Jovay Testnet Explorer', url: 'https://sepolia-explorer.jovay.io/l2' },
  },
  testnet: true,
})

export const config = createConfig({
  chains: [
    jovayTestnet,
  ],
  transports: {
    [jovayTestnet.id]: http(),
  },
  ssr: false,
})