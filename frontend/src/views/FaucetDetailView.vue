<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useAccount, useReadContract, useWriteContract, useWaitForTransactionReceipt } from '@wagmi/vue'
import { createPublicClient, http, parseUnits } from 'viem'
import { faucetRegistryAddress, ZERO_ADDRESS } from '../config/constants'
import { faucetRegistryAbi, jovayTestnet } from '../wagmi'

interface Props {
  address: string
}

interface FaucetInfo {
  tokenAddress: string
  distributionAmount: bigint
  totalDistributions: bigint
  remainingDistributions: bigint
  registrant: string
  isActive: boolean
  registrationTime: bigint
}

const props = defineProps<Props>()

const { address: userAddress, isConnected } = useAccount()

const faucetInfo = ref<FaucetInfo | null>(null)
const tokenName = ref('Unknown Token')
const tokenSymbol = ref('Unknown')
const tokenDecimals = ref(18)
const userBalance = ref<bigint>(0n)
const hasUserClaimed = ref(false)
const claimTime = ref<bigint>(0n)
const loading = ref(true)
const error = ref('')
const success = ref('')
const successTxHash = ref('')
const contributionAmount = ref('')
const isClaimingTokens = ref(false)
const isContributing = ref(false)

// Create public client for reading data without wallet connection
const publicClient = createPublicClient({
  chain: jovayTestnet,
  transport: http()
})

// Contract write hooks (only for contribution functionality)
const { writeContract: contribute, data: contributeHash, isPending: isContributePending } = useWriteContract()
const { writeContract: approveTokens, data: approveHash, isPending: isApprovePending } = useWriteContract()

// Transaction receipt hooks (only for contribution functionality)
const { isLoading: isContributeLoading, isSuccess: isContributeSuccess } = useWaitForTransactionReceipt({
  hash: contributeHash,
})
const { isLoading: isApproveLoading, isSuccess: isApproveSuccess } = useWaitForTransactionReceipt({
  hash: approveHash,
})

const isProcessing = computed(() => 
  isClaimingTokens.value ||
  isContributePending.value || isContributeLoading.value ||
  isApprovePending.value || isApproveLoading.value
)

const fetchFaucetInfo = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // Get faucet info
    const info = await publicClient.readContract({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'getFaucetInfo',
      args: [props.address as `0x${string}`]
    }) as FaucetInfo
    
    if (!info.isActive) {
      error.value = 'This faucet is not active'
      return
    }
    
    faucetInfo.value = info
    
    // Get token details
    try {
      const [name, symbol, decimals] = await Promise.all([
        publicClient.readContract({
          address: props.address as `0x${string}`,
          abi: [{"constant": true, "inputs": [], "name": "name", "outputs": [{"name": "", "type": "string"}], "type": "function"}],
          functionName: 'name'
        }) as Promise<string>,
        publicClient.readContract({
          address: props.address as `0x${string}`,
          abi: [{"constant": true, "inputs": [], "name": "symbol", "outputs": [{"name": "", "type": "string"}], "type": "function"}],
          functionName: 'symbol'
        }) as Promise<string>,
        publicClient.readContract({
          address: props.address as `0x${string}`,
          abi: [{"constant": true, "inputs": [], "name": "decimals", "outputs": [{"name": "", "type": "uint8"}], "type": "function"}],
          functionName: 'decimals'
        }) as Promise<number>
      ])
      
      tokenName.value = name
      tokenSymbol.value = symbol
      tokenDecimals.value = decimals
    } catch (err) {
      console.warn('Failed to fetch token details:', err)
    }
    
    // Check if user has claimed (if connected)
    if (isConnected.value && userAddress.value) {
      const [claimed, time] = await publicClient.readContract({
        address: faucetRegistryAddress as `0x${string}`,
        abi: faucetRegistryAbi,
        functionName: 'hasUserClaimed',
        args: [userAddress.value, props.address as `0x${string}`]
      }) as [boolean, bigint]
      
      hasUserClaimed.value = claimed
      claimTime.value = time
      
      // Get user balance
      try {
        const balance = await publicClient.readContract({
          address: props.address as `0x${string}`,
          abi: [{"constant": true, "inputs": [{"name": "_owner", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "balance", "type": "uint256"}], "type": "function"}],
          functionName: 'balanceOf',
          args: [userAddress.value]
        }) as bigint
        
        userBalance.value = balance
      } catch (err) {
        console.warn('Failed to fetch user balance:', err)
      }
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to fetch faucet information'
    console.error('Error fetching faucet info:', err)
  } finally {
    loading.value = false
  }
}

const handleClaimTokens = async () => {
  if (!isConnected.value || !userAddress.value) {
    error.value = 'Please connect your wallet first'
    return
  }
  
  if (hasUserClaimed.value) {
    error.value = 'You have already claimed tokens from this faucet'
    return
  }
  
  try {
    isClaimingTokens.value = true
    error.value = ''
    
    // Call backend API for claiming
    const response = await fetch('/api/claim', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: props.address,
        userAddress: userAddress.value
      })
    })
    
    const result = await response.json()
    
    if (result.success) {
      success.value = `Tokens claimed successfully!`
      successTxHash.value = result.transactionHash
      // Refresh data after successful claim
      setTimeout(() => {
        fetchFaucetInfo()
      }, 2000)
    } else {
      error.value = result.error || 'Failed to claim tokens'
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to claim tokens'
  } finally {
    isClaimingTokens.value = false
  }
}

const handleContribute = async () => {
  if (!isConnected.value || !userAddress.value) {
    error.value = 'Please connect your wallet first'
    return
  }
  
  if (!contributionAmount.value || Number(contributionAmount.value) <= 0) {
    error.value = 'Please enter a valid contribution amount'
    return
  }
  
  try {
    isContributing.value = true
    error.value = ''
    
    const contributionCount = BigInt(contributionAmount.value)
    const tokensNeeded = faucetInfo.value!.distributionAmount * contributionCount
    
    // Check allowance first
    const allowance = await publicClient.readContract({
      address: props.address as `0x${string}`,
      abi: [{"constant": true, "inputs": [{"name": "_owner", "type": "address"}, {"name": "_spender", "type": "address"}], "name": "allowance", "outputs": [{"name": "", "type": "uint256"}], "type": "function"}],
      functionName: 'allowance',
      args: [userAddress.value, faucetRegistryAddress as `0x${string}`]
    }) as bigint
    
    // If allowance is insufficient, approve first
    if (allowance < tokensNeeded) {
      approveTokens({
        address: props.address as `0x${string}`,
        abi: [{"constant": false, "inputs": [{"name": "_spender", "type": "address"}, {"name": "_value", "type": "uint256"}], "name": "approve", "outputs": [{"name": "", "type": "bool"}], "type": "function"}],
        functionName: 'approve',
        args: [faucetRegistryAddress as `0x${string}`, tokensNeeded]
      })
      return
    }
    
    // Proceed with contribution
    await contribute({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'contributeToFaucet',
      args: [props.address as `0x${string}`, contributionCount]
    })
  } catch (err: any) {
    error.value = err.message || 'Failed to contribute tokens'
  } finally {
    isContributing.value = false
  }
}

const formatAmount = (amount: bigint): string => {
  return (Number(amount) / Math.pow(10, tokenDecimals.value)).toFixed(4)
}

const formatAddress = (address: string): string => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`
}

const formatDate = (timestamp: bigint): string => {
  return new Date(Number(timestamp) * 1000).toLocaleDateString()
}

const contributionTokensNeeded = computed(() => {
  if (!contributionAmount.value || !faucetInfo.value) return '0'
  const count = Number(contributionAmount.value)
  if (isNaN(count) || count <= 0) return '0'
  const tokensNeeded = faucetInfo.value.distributionAmount * BigInt(count)
  return formatAmount(tokensNeeded)
})

const rateLimitInfo = ref({ canClaim: true, remainingTime: 0 })

const checkRateLimit = async () => {
  try {
    const response = await fetch('/api/rate-limit')
    const result = await response.json()
    rateLimitInfo.value = result
  } catch (err) {
    console.warn('Failed to check rate limit:', err)
    rateLimitInfo.value = { canClaim: true, remainingTime: 0 }
  }
}

// Watch for successful approval to continue with contribution
import { watch } from 'vue'
watch(isApproveSuccess, (success) => {
  if (success && isContributing.value) {
    // Retry contribution after approval
    const contributionCount = BigInt(contributionAmount.value)
    contribute({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'contributeToFaucet',
      args: [props.address as `0x${string}`, contributionCount]
    })
  }
})

// Watch for successful transactions to refresh data
watch(isContributeSuccess, () => {
  if (isContributeSuccess.value) {
    setTimeout(() => {
      fetchFaucetInfo()
    }, 2000) // Wait a bit for blockchain to update
  }
})

onMounted(() => {
  if (faucetRegistryAddress && faucetRegistryAddress !== ZERO_ADDRESS) {
    fetchFaucetInfo()
    checkRateLimit()
  } else {
    error.value = 'Faucet Registry contract not deployed. Please deploy the contract first.'
    loading.value = false
  }
})
</script>

<template>
  <div class="faucet-detail p-6 min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto">
      <div class="mb-6">
        <router-link to="/faucets" class="text-blue-600 hover:text-blue-800">
          ← Back to Faucets
        </router-link>
      </div>

      <div v-if="loading" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading faucet details...</p>
      </div>

      <div v-else-if="error && !faucetInfo" class="text-center py-8">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {{ error }}
        </div>
      </div>

      <div v-else-if="faucetInfo" class="space-y-6">
        <!-- Token Information Card -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center justify-between mb-4">
            <h1 class="text-2xl font-bold text-gray-900">{{ tokenName }}</h1>
            <div class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
              Active Faucet
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <h3 class="text-sm font-medium text-gray-500 mb-1">Token Symbol</h3>
              <p class="text-lg font-semibold">{{ tokenSymbol }}</p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-500 mb-1">Contract Address</h3>
              <p class="text-sm font-mono">{{ props.address }}</p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-500 mb-1">Amount per Claim</h3>
              <p class="text-lg font-semibold">{{ formatAmount(faucetInfo.distributionAmount) }} {{ tokenSymbol }}</p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-500 mb-1">Remaining Claims</h3>
              <p class="text-lg font-semibold">{{ faucetInfo.remainingDistributions.toString() }}</p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-500 mb-1">Total Claims Available</h3>
              <p class="text-lg font-semibold">{{ faucetInfo.totalDistributions.toString() }}</p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-500 mb-1">Registered By</h3>
              <p class="text-sm font-mono">{{ formatAddress(faucetInfo.registrant) }}</p>
            </div>
          </div>

          <div v-if="isConnected && userAddress">
            <div class="border-t pt-4">
              <h3 class="text-lg font-semibold mb-3">Your Status</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 class="text-sm font-medium text-gray-500 mb-1">Your Balance</h4>
                  <p class="text-lg font-semibold">{{ formatAmount(userBalance) }} {{ tokenSymbol }}</p>
                </div>
                <div>
                  <h4 class="text-sm font-medium text-gray-500 mb-1">Claim Status</h4>
                  <p class="text-lg font-semibold" :class="hasUserClaimed ? 'text-red-600' : 'text-green-600'">
                    {{ hasUserClaimed ? 'Already Claimed' : 'Available to Claim' }}
                  </p>
                  <p v-if="hasUserClaimed && claimTime > 0" class="text-xs text-gray-500">
                    Claimed on {{ formatDate(claimTime) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions Card -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Actions</h2>
          
          <div v-if="!isConnected" class="text-center py-4">
            <p class="text-gray-600 mb-4">Connect your wallet to claim tokens or contribute to the faucet</p>
            <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Connect Wallet
            </button>
          </div>

          <div v-else class="space-y-6">
            <!-- Claim Section -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h3 class="text-lg font-semibold mb-3">Claim Tokens</h3>
              <p class="text-gray-600 mb-4">
                Claim {{ formatAmount(faucetInfo.distributionAmount) }} {{ tokenSymbol }} tokens from this faucet
              </p>
              
              <div v-if="error" class="mb-4">
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                  {{ error }}
                </div>
              </div>

              <div v-if="success" class="mb-4">
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                  <p>{{ success }}</p>
                  <a 
                    v-if="successTxHash"
                    :href="`${jovayTestnet.blockExplorers.default.url}/tx/${successTxHash}`" 
                    target="_blank" 
                    class="text-blue-600 hover:text-blue-800 underline"
                  >
                    View on Block Explorer
                  </a>
                </div>
              </div>
              
              <div class="flex justify-center">
                <button 
                  @click="handleClaimTokens"
                  :disabled="hasUserClaimed || faucetInfo.remainingDistributions <= 0 || isProcessing || !rateLimitInfo.canClaim"
                  class="bg-green-600 text-white hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                  style="padding: 5px 10px; border-radius: 20px;"
                >
                <span v-if="isClaimingTokens">Claiming...</span>
                <span v-else-if="!rateLimitInfo.canClaim">Wait {{ rateLimitInfo.remainingTime }}s</span>
                <span v-else-if="hasUserClaimed">Already Claimed</span>
                <span v-else-if="faucetInfo.remainingDistributions <= 0">No Claims Available</span>
                  <span v-else>Claim {{ formatAmount(faucetInfo.distributionAmount) }} {{ tokenSymbol }}</span>
                </button>
              </div>
              
              <div v-if="!rateLimitInfo.canClaim" class="mt-2 text-sm text-orange-600">
                Rate limit active. You can claim again in {{ rateLimitInfo.remainingTime }} seconds.
              </div>
            </div>

            <!-- Contribute Section -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h3 class="text-lg font-semibold mb-3">Contribute to Faucet</h3>
              <p class="text-gray-600 mb-4">
                Add more claims to this faucet by contributing your own {{ tokenSymbol }} tokens
              </p>
              
              <div class="flex items-end gap-4 mb-4">
                <div class="flex-1">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Number of Additional Claims
                  </label>
                  <input 
                    v-model="contributionAmount"
                    type="number"
                    min="1"
                    placeholder="Enter number of claims"
                    class="w-full border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    style="padding: 5px 10px;"
                    @wheel.prevent
                  />
                </div>
                <div class="text-sm text-gray-600">
                  <div>Tokens needed:</div>
                  <div class="font-semibold">{{ contributionTokensNeeded }} {{ tokenSymbol }}</div>
                </div>
              </div>

              <div v-if="isContributeSuccess" class="mb-4">
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                  Contribution successful!
                </div>
              </div>
              
              <div class="flex justify-center">
                <button 
                  @click="handleContribute"
                  :disabled="!contributionAmount || Number(contributionAmount) <= 0 || isProcessing"
                  class="bg-blue-600 text-white hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                  style="padding: 5px 10px; border-radius: 20px;"
                >
                <span v-if="isApprovePending || isApproveLoading">Approving...</span>
                <span v-else-if="isContributePending || isContributeLoading">Contributing...</span>
                  <span v-else>Contribute {{ contributionAmount || '0' }} Claims</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>