<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useReadContract, useAccount } from '@wagmi/vue'
import { createPublicClient, http } from 'viem'
import { faucetRegistryAddress, ZERO_ADDRESS } from '../config/constants'
import { faucetRegistryAbi, jovayTestnet } from '../wagmi'
import { RouterLink } from 'vue-router'

interface FaucetInfo {
  tokenAddress: string
  distributionAmount: bigint
  totalDistributions: bigint
  remainingDistributions: bigint
  registrant: string
  isActive: boolean
  registrationTime: bigint
  tokenName?: string
  tokenSymbol?: string
}

const faucets = ref<FaucetInfo[]>([])
const loading = ref(true)
const error = ref<string>('')
const currentPage = ref(0)
const pageSize = 10
const totalFaucets = ref(0)

const { isConnected } = useAccount()

// Create public client for reading data without wallet connection
const publicClient = createPublicClient({
  chain: jovayTestnet,
  transport: http()
})

const fetchFaucets = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // Use public client to read data without wallet connection
    const result = await publicClient.readContract({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'getActiveFaucetsPaginated',
      args: [BigInt(currentPage.value * pageSize), BigInt(pageSize)]
    }) as [FaucetInfo[], bigint]
    
    const faucetList = result[0]
    totalFaucets.value = Number(result[1])
    
    // Fetch token names and symbols for each faucet
    const faucetsWithTokenInfo = await Promise.all(
      faucetList.map(async (faucet) => {
        const [tokenName, tokenSymbol] = await Promise.all([
          getTokenName(faucet.tokenAddress),
          getTokenSymbol(faucet.tokenAddress)
        ])
        return {
          ...faucet,
          tokenName,
          tokenSymbol
        }
      })
    )
    
    faucets.value = faucetsWithTokenInfo
  } catch (err: any) {
    error.value = err.message || 'Failed to fetch faucets'
    console.error('Error fetching faucets:', err)
  } finally {
    loading.value = false
  }
}

const getTokenSymbol = async (tokenAddress: string): Promise<string> => {
  try {
    const symbol = await publicClient.readContract({
      address: tokenAddress as `0x${string}`,
      abi: [
        {
          "constant": true,
          "inputs": [],
          "name": "symbol",
          "outputs": [{"name": "", "type": "string"}],
          "type": "function"
        }
      ],
      functionName: 'symbol'
    }) as string
    return symbol
  } catch {
    return 'Unknown'
  }
}

const getTokenName = async (tokenAddress: string): Promise<string> => {
  try {
    const name = await publicClient.readContract({
      address: tokenAddress as `0x${string}`,
      abi: [
        {
          "constant": true,
          "inputs": [],
          "name": "name",
          "outputs": [{"name": "", "type": "string"}],
          "type": "function"
        }
      ],
      functionName: 'name'
    }) as string
    return name
  } catch {
    return 'Unknown Token'
  }
}

const formatAmount = (amount: bigint): string => {
  return (Number(amount) / 1e18).toFixed(4)
}

const formatAddress = (address: string): string => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`
}

const nextPage = () => {
  if ((currentPage.value + 1) * pageSize < totalFaucets.value) {
    currentPage.value++
    fetchFaucets()
  }
}

const prevPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--
    fetchFaucets()
  }
}

onMounted(() => {
  if (faucetRegistryAddress && faucetRegistryAddress !== ZERO_ADDRESS) {
    fetchFaucets()
  } else {
    error.value = 'Faucet Registry contract not deployed. Please deploy the contract first.'
    loading.value = false
  }
})
</script>

<template>
  <div class="faucet-list">
    <div class="page-header">
      <div class="header-text">
        <h1 class="page-title">🌸 ERC20 Faucets</h1>
        <p class="page-description">Claim free ERC20 tokens from available faucets</p>
      </div>
      <div class="header-actions">
        <RouterLink to="/register" class="btn btn-primary">
          ✨ Register Faucet
        </RouterLink>
        <RouterLink to="/admin" class="btn btn-secondary">
          ⚙️ Admin Panel
        </RouterLink>
      </div>
    </div>

    <div v-if="loading" class="loading-section">
      <div class="loading-spinner"></div>
      <p class="loading-text">Loading faucets...</p>
    </div>

    <div v-else-if="error" class="error-section">
      <div class="error-card">
        <div class="error-icon">⚠️</div>
        <div class="error-message">{{ error }}</div>
      </div>
    </div>

    <div v-else-if="faucets.length === 0" class="empty-section">
      <div class="empty-card">
        <div class="empty-icon">🔍</div>
        <div class="empty-message">No active faucets available at the moment.</div>
      </div>
    </div>

    <div v-else class="faucets-content">
      <div class="faucets-grid">
        <RouterLink 
          v-for="faucet in faucets" 
          :key="faucet.tokenAddress"
          :to="`/faucet/${faucet.tokenAddress}`"
          class="faucet-card"
        >
          <div class="card-header">
            <div class="token-symbol">{{ faucet.tokenSymbol || '?' }}</div>
            <div class="status-badge status-success">Active</div>
          </div>
          
          <div class="card-body">
            <h3 class="token-name">{{ faucet.tokenName || 'Unknown Token' }}</h3>
            <p class="token-address">{{ formatAddress(faucet.tokenAddress) }}</p>

            <div class="token-stats">
              <div class="stat-item">
                <span class="stat-label">Per claim:</span>
                <span class="stat-value">{{ formatAmount(faucet.distributionAmount) }} {{ faucet.tokenSymbol || '' }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Remaining:</span>
                <span class="stat-value">{{ faucet.remainingDistributions.toString() }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Total claims:</span>
                <span class="stat-value">{{ faucet.totalDistributions.toString() }}</span>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <div class="claim-button">
              <span>💧 Claim Tokens</span>
            </div>
          </div>
        </RouterLink>
      </div>

      <div class="pagination">
        <button 
          @click="prevPage"
          :disabled="currentPage === 0"
          class="btn btn-secondary"
          :class="{ disabled: currentPage === 0 }"
        >
          ← Previous
        </button>
        
        <span class="page-info">
          Page {{ currentPage + 1 }} of {{ Math.ceil(totalFaucets / pageSize) }}
        </span>
        
        <button 
          @click="nextPage"
          :disabled="(currentPage + 1) * pageSize >= totalFaucets"
          class="btn btn-secondary"
          :class="{ disabled: (currentPage + 1) * pageSize >= totalFaucets }"
        >
          Next →
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.faucet-list {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 3rem;
  gap: 2rem;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 1.125rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.header-actions {
  display: flex;
  gap: 1rem;
  flex-shrink: 0;
}

.loading-section,
.error-section,
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-text {
  margin-top: 1rem;
  color: var(--text-secondary);
  font-size: 1.125rem;
}

.error-card,
.empty-card {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
  max-width: 400px;
}

.error-icon,
.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-message {
  color: var(--danger-color);
  font-size: 1.125rem;
  font-weight: 500;
}

.empty-message {
  color: var(--text-secondary);
  font-size: 1.125rem;
}

.faucets-content {
  margin-bottom: 3rem;
}

.faucets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.faucet-card {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.faucet-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-color);
}

.faucet-card .card-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--bg-accent), var(--bg-secondary));
  border-bottom: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.token-symbol {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.faucet-card .card-body {
  padding: 1.5rem;
  flex: 1;
}

.token-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.token-address {
  color: var(--text-light);
  font-size: 0.875rem;
  font-family: monospace;
  margin-bottom: 1.5rem;
}

.token-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-light);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.faucet-card .card-footer {
  padding: 1rem 1.5rem;
  background: var(--bg-accent);
  border-top: 1px solid var(--border-light);
}

.claim-button {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius-sm);
  text-align: center;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.faucet-card:hover .claim-button {
  transform: translateY(-1px);
  box-shadow: var(--shadow-soft);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-top: 2rem;
}

.page-info {
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.75rem 1rem;
  background: var(--bg-accent);
  border-radius: var(--border-radius-sm);
}

.btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .page-title {
    font-size: 2rem;
    text-align: center;
  }
  
  .page-description {
    text-align: center;
  }
  
  .faucets-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .pagination {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .faucet-card .card-header,
  .faucet-card .card-body,
  .faucet-card .card-footer {
    padding: 1rem;
  }
  
  .token-symbol {
    font-size: 1.25rem;
  }
  
  .token-name {
    font-size: 1.125rem;
  }
}
</style>