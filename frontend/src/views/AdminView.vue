<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useAccount, useReadContract, useWriteContract, useWaitForTransactionReceipt } from '@wagmi/vue'
import { createPublicClient, http, isAddress } from 'viem'
import { faucetRegistryAddress, ZERO_ADDRESS } from '../config/constants'
import { faucetRegistryAbi, jovayTestnet } from '../wagmi'

interface FaucetInfo {
  tokenAddress: string
  distributionAmount: bigint
  totalDistributions: bigint
  remainingDistributions: bigint
  registrant: string
  isActive: boolean
  registrationTime: bigint
}

const { address: userAddress, isConnected } = useAccount()

const isOwner = ref(false)
const registrationEnabled = ref(true)
const faucets = ref<FaucetInfo[]>([])
const totalFaucets = ref(0)
const loading = ref(true)
const error = ref('')
const success = ref('')

// Form states
const distributionRecipient = ref('')
const selectedTokenForDistribution = ref('')
const blacklistAddress = ref('')
const blacklistAction = ref<'add' | 'remove'>('add')
const newOwnerAddress = ref('')

// Create public client for reading data
const publicClient = createPublicClient({
  chain: jovayTestnet,
  transport: http()
})

// Contract write hooks
const { writeContract: toggleRegistration, data: toggleHash, isPending: isTogglePending } = useWriteContract()
const { writeContract: setBlacklist, data: blacklistHash, isPending: isBlacklistPending } = useWriteContract()
const { writeContract: distributeTokens, data: distributeHash, isPending: isDistributePending } = useWriteContract()
const { writeContract: transferOwnership, data: transferHash, isPending: isTransferPending } = useWriteContract()

// Transaction receipt hooks
const { isLoading: isToggleLoading, isSuccess: isToggleSuccess } = useWaitForTransactionReceipt({
  hash: toggleHash,
})
const { isLoading: isBlacklistLoading, isSuccess: isBlacklistSuccess } = useWaitForTransactionReceipt({
  hash: blacklistHash,
})
const { isLoading: isDistributeLoading, isSuccess: isDistributeSuccess } = useWaitForTransactionReceipt({
  hash: distributeHash,
})
const { isLoading: isTransferLoading, isSuccess: isTransferSuccess } = useWaitForTransactionReceipt({
  hash: transferHash,
})

const isProcessing = computed(() => 
  isTogglePending.value || isToggleLoading.value ||
  isBlacklistPending.value || isBlacklistLoading.value ||
  isDistributePending.value || isDistributeLoading.value ||
  isTransferPending.value || isTransferLoading.value
)

// Read contract data
const { data: ownerData } = useReadContract({
  address: faucetRegistryAddress as `0x${string}`,
  abi: faucetRegistryAbi,
  functionName: 'owner'
})

const { data: registrationEnabledData } = useReadContract({
  address: faucetRegistryAddress as `0x${string}`,
  abi: faucetRegistryAbi,
  functionName: 'registrationEnabled'
})

const checkOwnership = () => {
  if (ownerData.value && userAddress.value) {
    isOwner.value = (ownerData.value as string).toLowerCase() === userAddress.value.toLowerCase()
  }
}

const fetchFaucets = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const result = await publicClient.readContract({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'getFaucetsPaginated',
      args: [BigInt(0), BigInt(100)] // Get first 100 faucets
    }) as [FaucetInfo[], bigint]
    
    faucets.value = result[0]
    totalFaucets.value = Number(result[1])
    
    if (registrationEnabledData.value !== undefined) {
      registrationEnabled.value = registrationEnabledData.value as boolean
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to fetch faucets'
    console.error('Error fetching faucets:', err)
  } finally {
    loading.value = false
  }
}

const handleToggleRegistration = async () => {
  if (!isOwner.value) return

  try {
    error.value = ''
    success.value = ''
    
    await toggleRegistration({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'toggleRegistration',
      args: [!registrationEnabled.value]
    })
  } catch (err: any) {
    error.value = err.message || 'Failed to toggle registration'
  }
}

const handleBlacklist = async () => {
  if (!isOwner.value || !blacklistAddress.value || !isAddress(blacklistAddress.value)) return

  try {
    error.value = ''
    success.value = ''
    
    await setBlacklist({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'setFaucetBlacklist',
      args: [blacklistAddress.value as `0x${string}`, blacklistAction.value === 'add']
    })
  } catch (err: any) {
    error.value = err.message || 'Failed to update blacklist'
  }
}

const handleDistributeTokens = async () => {
  if (!isOwner.value || !selectedTokenForDistribution.value || !distributionRecipient.value || !isAddress(distributionRecipient.value)) return

  try {
    error.value = ''
    success.value = ''
    
    await distributeTokens({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'distributeTokens',
      args: [selectedTokenForDistribution.value as `0x${string}`, distributionRecipient.value as `0x${string}`]
    })
  } catch (err: any) {
    error.value = err.message || 'Failed to distribute tokens'
  }
}

const handleTransferOwnership = async () => {
  if (!isOwner.value || !newOwnerAddress.value || !isAddress(newOwnerAddress.value)) return

  const confirmed = confirm('Are you sure you want to transfer ownership? This action cannot be undone.')
  if (!confirmed) return

  try {
    error.value = ''
    success.value = ''
    
    await transferOwnership({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'transferOwnership',
      args: [newOwnerAddress.value as `0x${string}`]
    })
  } catch (err: any) {
    error.value = err.message || 'Failed to transfer ownership'
  }
}

const formatAmount = (amount: bigint): string => {
  return (Number(amount) / 1e18).toFixed(4)
}

const formatAddress = (address: string): string => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`
}

const formatDate = (timestamp: bigint): string => {
  return new Date(Number(timestamp) * 1000).toLocaleDateString()
}

// Watch for successful transactions
import { watch } from 'vue'
watch([isToggleSuccess, isBlacklistSuccess, isDistributeSuccess], () => {
  if (isToggleSuccess.value) {
    success.value = 'Registration status updated successfully!'
    registrationEnabled.value = !registrationEnabled.value
  }
  if (isBlacklistSuccess.value) {
    success.value = `Faucet ${blacklistAction.value === 'add' ? 'blacklisted' : 'removed from blacklist'} successfully!`
    blacklistAddress.value = ''
  }
  if (isDistributeSuccess.value) {
    success.value = 'Tokens distributed successfully!'
    distributionRecipient.value = ''
    selectedTokenForDistribution.value = ''
    setTimeout(() => fetchFaucets(), 2000)
  }
})

watch(isTransferSuccess, () => {
  if (isTransferSuccess.value) {
    success.value = 'Ownership transferred successfully!'
    isOwner.value = false
    newOwnerAddress.value = ''
  }
})

// Watch for address and owner data changes
watch([userAddress, ownerData], checkOwnership)

onMounted(() => {
  if (faucetRegistryAddress && faucetRegistryAddress !== ZERO_ADDRESS) {
    fetchFaucets()
    checkOwnership()
  } else {
    error.value = 'Faucet Registry contract not deployed. Please deploy the contract first.'
    loading.value = false
  }
})
</script>

<template>
  <div class="admin-panel p-6 min-h-screen bg-gray-50">
    <div class="max-w-6xl mx-auto">
      <div class="mb-6">
        <router-link to="/faucets" class="text-blue-600 hover:text-blue-800">
          ← Back to Faucets
        </router-link>
      </div>

      <h1 class="text-3xl font-bold text-gray-900 mb-6">Admin Panel</h1>

      <div v-if="!isConnected" class="text-center py-8">
        <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
          Connect your wallet to access the admin panel
        </div>
      </div>

      <div v-else-if="!isOwner" class="text-center py-8">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Access denied. You are not the owner of this contract.
        </div>
      </div>

      <div v-else class="space-y-6">
        <!-- Status Messages -->
        <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {{ error }}
        </div>

        <div v-if="success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {{ success }}
        </div>

        <!-- Contract Status -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Contract Status</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h3 class="text-sm font-medium text-gray-500 mb-1">Total Faucets</h3>
              <p class="text-2xl font-bold text-gray-900">{{ totalFaucets }}</p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-500 mb-1">Registration Status</h3>
              <p class="text-2xl font-bold" :class="registrationEnabled ? 'text-green-600' : 'text-red-600'">
                {{ registrationEnabled ? 'Enabled' : 'Disabled' }}
              </p>
            </div>
            <div>
              <h3 class="text-sm font-medium text-gray-500 mb-1">Contract Owner</h3>
              <p class="text-sm font-mono text-gray-900">{{ formatAddress((ownerData as string) || '') }}</p>
            </div>
          </div>
        </div>

        <!-- Registration Control -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Registration Control</h2>
          <p class="text-gray-600 mb-4">
            Control whether new faucets can be registered by users.
          </p>
          <button 
            @click="handleToggleRegistration"
            :disabled="isProcessing"
            class="px-6 py-2 rounded-lg font-medium transition-colors"
            :class="registrationEnabled 
              ? 'bg-red-600 text-white hover:bg-red-700 disabled:bg-gray-400' 
              : 'bg-green-600 text-white hover:bg-green-700 disabled:bg-gray-400'"
          >
            <span v-if="isTogglePending || isToggleLoading">Processing...</span>
            <span v-else>{{ registrationEnabled ? 'Disable Registration' : 'Enable Registration' }}</span>
          </button>
        </div>

        <!-- Token Distribution -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Distribute Tokens</h2>
          <p class="text-gray-600 mb-4">
            Manually distribute tokens from any faucet to a specific recipient.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Select Token
              </label>
              <select 
                v-model="selectedTokenForDistribution"
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a token...</option>
                <option 
                  v-for="faucet in faucets.filter(f => f.isActive && f.remainingDistributions > 0)"
                  :key="faucet.tokenAddress"
                  :value="faucet.tokenAddress"
                >
                  {{ formatAddress(faucet.tokenAddress) }} ({{ faucet.remainingDistributions.toString() }} remaining)
                </option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Recipient Address
              </label>
              <input 
                v-model="distributionRecipient"
                type="text"
                placeholder="0x..."
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                :class="{ 'border-red-500': distributionRecipient && !isAddress(distributionRecipient) }"
              />
            </div>
          </div>
          
          <button 
            @click="handleDistributeTokens"
            :disabled="!selectedTokenForDistribution || !distributionRecipient || !isAddress(distributionRecipient) || isProcessing"
            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            <span v-if="isDistributePending || isDistributeLoading">Distributing...</span>
            <span v-else>Distribute Tokens</span>
          </button>
        </div>

        <!-- Blacklist Management -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Blacklist Management</h2>
          <p class="text-gray-600 mb-4">
            Add or remove faucets from the blacklist to prevent them from being used.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Token Address
              </label>
              <input 
                v-model="blacklistAddress"
                type="text"
                placeholder="0x..."
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                :class="{ 'border-red-500': blacklistAddress && !isAddress(blacklistAddress) }"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Action
              </label>
              <select 
                v-model="blacklistAction"
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="add">Add to Blacklist</option>
                <option value="remove">Remove from Blacklist</option>
              </select>
            </div>
            <div class="flex items-end">
              <button 
                @click="handleBlacklist"
                :disabled="!blacklistAddress || !isAddress(blacklistAddress) || isProcessing"
                class="w-full px-6 py-2 rounded-lg font-medium transition-colors"
                :class="blacklistAction === 'add' 
                  ? 'bg-red-600 text-white hover:bg-red-700 disabled:bg-gray-400' 
                  : 'bg-green-600 text-white hover:bg-green-700 disabled:bg-gray-400'"
              >
                <span v-if="isBlacklistPending || isBlacklistLoading">Processing...</span>
                <span v-else>{{ blacklistAction === 'add' ? 'Blacklist' : 'Unblacklist' }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Ownership Transfer -->
        <div class="bg-white rounded-lg shadow-md p-6 border-2 border-red-200">
          <h2 class="text-xl font-bold mb-4 text-red-800">Transfer Ownership</h2>
          <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            <strong>Warning:</strong> This action is irreversible. You will lose admin access to this contract.
          </div>
          
          <div class="flex gap-4 mb-4">
            <div class="flex-1">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                New Owner Address
              </label>
              <input 
                v-model="newOwnerAddress"
                type="text"
                placeholder="0x..."
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                :class="{ 'border-red-500': newOwnerAddress && !isAddress(newOwnerAddress) }"
              />
            </div>
            <div class="flex items-end">
              <button 
                @click="handleTransferOwnership"
                :disabled="!newOwnerAddress || !isAddress(newOwnerAddress) || isProcessing"
                class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                <span v-if="isTransferPending || isTransferLoading">Transferring...</span>
                <span v-else>Transfer Ownership</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Faucets List -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">All Registered Faucets</h2>
          
          <div v-if="loading" class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
          
          <div v-else-if="faucets.length === 0" class="text-center py-4 text-gray-600">
            No faucets registered yet.
          </div>
          
          <div v-else class="overflow-x-auto">
            <table class="w-full text-sm">
              <thead>
                <tr class="border-b border-gray-200">
                  <th class="text-left py-2">Token Address</th>
                  <th class="text-left py-2">Distribution Amount</th>
                  <th class="text-left py-2">Remaining/Total</th>
                  <th class="text-left py-2">Registrant</th>
                  <th class="text-left py-2">Status</th>
                  <th class="text-left py-2">Registered</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="faucet in faucets" :key="faucet.tokenAddress" class="border-b border-gray-100">
                  <td class="py-2 font-mono text-xs">{{ formatAddress(faucet.tokenAddress) }}</td>
                  <td class="py-2">{{ formatAmount(faucet.distributionAmount) }}</td>
                  <td class="py-2">{{ faucet.remainingDistributions.toString() }}/{{ faucet.totalDistributions.toString() }}</td>
                  <td class="py-2 font-mono text-xs">{{ formatAddress(faucet.registrant) }}</td>
                  <td class="py-2">
                    <span 
                      class="px-2 py-1 text-xs rounded"
                      :class="faucet.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                    >
                      {{ faucet.isActive ? 'Active' : 'Inactive' }}
                    </span>
                  </td>
                  <td class="py-2 text-xs">{{ formatDate(faucet.registrationTime) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>