<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAccount, useWriteContract, useWaitForTransactionReceipt } from '@wagmi/vue'
import { createPublicClient, http, parseUnits, isAddress } from 'viem'
import { faucetRegistryAddress } from '../config/constants'
import { faucetRegistryAbi, jovayTestnet } from '../wagmi'

const { address: userAddress, isConnected } = useAccount()

const tokenAddress = ref('')
const distributionAmount = ref('')
const totalDistributions = ref('')
const tokenName = ref('')
const tokenSymbol = ref('')
const tokenDecimals = ref(18)
const userBalance = ref<bigint>(0n)
const allowance = ref<bigint>(0n)
const loading = ref(false)
const error = ref('')
const success = ref('')

// Create public client for reading data
const publicClient = createPublicClient({
  chain: jovayTestnet,
  transport: http()
})

// Contract write hooks
const { writeContract: registerFaucet, data: registerHash, isPending: isRegisterPending } = useWriteContract()
const { writeContract: approveTokens, data: approveHash, isPending: isApprovePending } = useWriteContract()

// Transaction receipt hooks
const { isLoading: isRegisterLoading, isSuccess: isRegisterSuccess } = useWaitForTransactionReceipt({
  hash: registerHash,
})
const { isLoading: isApproveLoading, isSuccess: isApproveSuccess } = useWaitForTransactionReceipt({
  hash: approveHash,
})

const isProcessing = computed(() => 
  isRegisterPending.value || isRegisterLoading.value || 
  isApprovePending.value || isApproveLoading.value || loading.value
)

const fetchTokenInfo = async () => {
  if (!tokenAddress.value || !isAddress(tokenAddress.value)) {
    tokenName.value = ''
    tokenSymbol.value = ''
    tokenDecimals.value = 18
    userBalance.value = 0n
    allowance.value = 0n
    return
  }

  try {
    loading.value = true
    error.value = ''

    // Get token info
    const [name, symbol, decimals] = await Promise.all([
      publicClient.readContract({
        address: tokenAddress.value as `0x${string}`,
        abi: [{"constant": true, "inputs": [], "name": "name", "outputs": [{"name": "", "type": "string"}], "type": "function"}],
        functionName: 'name'
      }) as Promise<string>,
      publicClient.readContract({
        address: tokenAddress.value as `0x${string}`,
        abi: [{"constant": true, "inputs": [], "name": "symbol", "outputs": [{"name": "", "type": "string"}], "type": "function"}],
        functionName: 'symbol'
      }) as Promise<string>,
      publicClient.readContract({
        address: tokenAddress.value as `0x${string}`,
        abi: [{"constant": true, "inputs": [], "name": "decimals", "outputs": [{"name": "", "type": "uint8"}], "type": "function"}],
        functionName: 'decimals'
      }) as Promise<number>
    ])

    tokenName.value = name
    tokenSymbol.value = symbol
    tokenDecimals.value = decimals

    // Get user balance and allowance if connected
    if (isConnected.value && userAddress.value) {
      const [balance, currentAllowance] = await Promise.all([
        publicClient.readContract({
          address: tokenAddress.value as `0x${string}`,
          abi: [{"constant": true, "inputs": [{"name": "_owner", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "balance", "type": "uint256"}], "type": "function"}],
          functionName: 'balanceOf',
          args: [userAddress.value]
        }) as Promise<bigint>,
        publicClient.readContract({
          address: tokenAddress.value as `0x${string}`,
          abi: [{"constant": true, "inputs": [{"name": "_owner", "type": "address"}, {"name": "_spender", "type": "address"}], "name": "allowance", "outputs": [{"name": "", "type": "uint256"}], "type": "function"}],
          functionName: 'allowance',
          args: [userAddress.value, faucetRegistryAddress as `0x${string}`]
        }) as Promise<bigint>
      ])

      userBalance.value = balance
      allowance.value = currentAllowance
    }
  } catch (err: any) {
    error.value = 'Failed to fetch token information. Please check the token address.'
    console.error('Error fetching token info:', err)
  } finally {
    loading.value = false
  }
}

const totalTokensNeeded = computed(() => {
  if (!distributionAmount.value || !totalDistributions.value || tokenDecimals.value === undefined) {
    return 0n
  }
  try {
    const distributionAmountStr = distributionAmount.value.toString().trim()
    const totalDistributionsStr = totalDistributions.value.toString().trim()
    
    // Validate inputs
    if (isNaN(Number(distributionAmountStr)) || Number(distributionAmountStr) <= 0) {
      return 0n
    }
    
    if (isNaN(Number(totalDistributionsStr)) || Number(totalDistributionsStr) <= 0 || !Number.isInteger(Number(totalDistributionsStr))) {
      return 0n
    }
    
    const amount = parseUnits(distributionAmountStr, tokenDecimals.value)
    const count = BigInt(totalDistributionsStr)
    return amount * count
  } catch (error) {
    console.warn('Error calculating total tokens needed:', error)
    return 0n
  }
})

const formatAmount = (amount: bigint): string => {
  return (Number(amount) / Math.pow(10, tokenDecimals.value)).toFixed(4)
}

const needsApproval = computed(() => {
  return totalTokensNeeded.value > allowance.value
})

const hasInsufficientBalance = computed(() => {
  return totalTokensNeeded.value > userBalance.value
})

const canRegister = computed(() => {
  return (
    isConnected.value &&
    tokenAddress.value &&
    isAddress(tokenAddress.value) &&
    distributionAmount.value &&
    totalDistributions.value &&
    Number(distributionAmount.value) > 0 &&
    Number(totalDistributions.value) > 0 &&
    !hasInsufficientBalance.value &&
    !isProcessing.value
  )
})

const checkAllowance = async () => {
  if (isConnected.value && userAddress.value && tokenAddress.value && isAddress(tokenAddress.value)) {
    try {
      const currentAllowance = await publicClient.readContract({
        address: tokenAddress.value as `0x${string}`,
        abi: [{"constant": true, "inputs": [{"name": "_owner", "type": "address"}, {"name": "_spender", "type": "address"}], "name": "allowance", "outputs": [{"name": "", "type": "uint256"}], "type": "function"}],
        functionName: 'allowance',
        args: [userAddress.value, faucetRegistryAddress as `0x${string}`]
      }) as bigint
      allowance.value = currentAllowance
    } catch (err) {
      console.error('Failed to check allowance:', err)
    }
  }
}

const handleApprove = async () => {
  if (!canRegister.value) return

  try {
    error.value = ''
    await approveTokens({
      address: tokenAddress.value as `0x${string}`,
      abi: [{"constant": false, "inputs": [{"name": "_spender", "type": "address"}, {"name": "_value", "type": "uint256"}], "name": "approve", "outputs": [{"name": "", "type": "bool"}], "type": "function"}],
      functionName: 'approve',
      args: [faucetRegistryAddress as `0x${string}`, totalTokensNeeded.value]
    })
  } catch (err: any) {
    error.value = err.message || 'Failed to approve tokens'
  }
}

const handleRegister = async () => {
  if (!canRegister.value || needsApproval.value) return

  try {
    error.value = ''
    success.value = ''

    console.log('clicked Register button', distributionAmount.value, tokenDecimals.value, totalDistributions.value)
    
    // Validate inputs
    if (!distributionAmount.value || !totalDistributions.value || tokenDecimals.value === undefined) {
      error.value = 'Please fill in all required fields'
      return
    }
    
    const distributionAmountStr = distributionAmount.value.toString().trim()
    const totalDistributionsStr = totalDistributions.value.toString().trim()
    
    // Validate numeric inputs
    if (isNaN(Number(distributionAmountStr)) || Number(distributionAmountStr) <= 0) {
      error.value = 'Distribution amount must be a positive number'
      return
    }
    
    if (isNaN(Number(totalDistributionsStr)) || Number(totalDistributionsStr) <= 0 || !Number.isInteger(Number(totalDistributionsStr))) {
      error.value = 'Total distributions must be a positive integer'
      return
    }
    
    let amount: bigint
    let count: bigint
    
    try {
      amount = parseUnits(distributionAmountStr, tokenDecimals.value)
      count = BigInt(totalDistributionsStr)
    } catch (parseError: any) {
      error.value = 'Invalid number format: ' + parseError.message
      return
    }
    
    console.log('amount', amount, 'count', count)
    
    // Check for potential overflow (optional safety check)
    try {
      const totalNeeded = amount * count
      console.log('totalNeeded', totalNeeded)
    } catch (overflowError: any) {
      error.value = 'Numbers too large: ' + overflowError.message
      return
    }

    await registerFaucet({
      address: faucetRegistryAddress as `0x${string}`,
      abi: faucetRegistryAbi,
      functionName: 'registerFaucet',
      args: [tokenAddress.value as `0x${string}`, amount, count]
    })
  } catch (err: any) {
    console.error('Registration error:', err)
    error.value = err.message || 'Failed to register faucet'
  }
}

// Watch for token address changes
import { watch } from 'vue'
watch(tokenAddress, () => {
  fetchTokenInfo()
  checkAllowance()
})

// Watch for successful approval to update allowance
watch(isApproveSuccess, async (success) => {
  if (success && tokenAddress.value && isConnected.value && userAddress.value) {
    // Refresh allowance
    try {
      const newAllowance = await publicClient.readContract({
        address: tokenAddress.value as `0x${string}`,
        abi: [{"constant": true, "inputs": [{"name": "_owner", "type": "address"}, {"name": "_spender", "type": "address"}], "name": "allowance", "outputs": [{"name": "", "type": "uint256"}], "type": "function"}],
        functionName: 'allowance',
        args: [userAddress.value, faucetRegistryAddress as `0x${string}`]
      }) as bigint
      
      allowance.value = newAllowance
    } catch (err) {
      console.error('Failed to update allowance:', err)
    }
  }
})

// Watch for successful registration
watch(isRegisterSuccess, (isSuccess) => {
  if (isSuccess) {
    success.value = 'Faucet registered successfully!'
    // Reset form
    tokenAddress.value = ''
    distributionAmount.value = ''
    totalDistributions.value = ''
    tokenName.value = ''
    tokenSymbol.value = ''
    tokenDecimals.value = 18
    userBalance.value = 0n
    allowance.value = 0n
  }
})
</script>

<template>
  <div class="register-faucet p-6 min-h-screen bg-gray-50">
    <div class="max-w-2xl mx-auto">
      <div class="mb-6">
        <router-link to="/faucets" class="text-blue-600 hover:text-blue-800">
          ← Back to Faucets
        </router-link>
      </div>

      <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">Register New Faucet</h1>
        
        <div v-if="!isConnected" class="text-center py-8">
          <p class="text-gray-600 mb-4">Connect your wallet to register a new faucet</p>
          <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Connect Wallet
          </button>
        </div>

        <form v-else @submit.prevent class="space-y-6">
          <!-- Token Address -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              ERC20 Token Contract Address
            </label>
            <input 
              v-model="tokenAddress"
              type="text"
              placeholder="0x..."
              class="w-full border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              style="padding: 5px 10px;"
              :class="{ 'border-red-500': tokenAddress && !isAddress(tokenAddress) }"
            />
            <p v-if="tokenAddress && !isAddress(tokenAddress)" class="text-red-600 text-sm mt-1">
              Please enter a valid Ethereum address
            </p>
          </div>

          <!-- Token Info Display -->
          <div v-if="tokenName && tokenSymbol" class="bg-gray-50 rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-2">Token Information</h3>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-600">Name:</span>
                <span class="ml-2 font-medium">{{ tokenName }}</span>
              </div>
              <div>
                <span class="text-gray-600">Symbol:</span>
                <span class="ml-2 font-medium">{{ tokenSymbol }}</span>
              </div>
              <div>
                <span class="text-gray-600">Your Balance:</span>
                <span class="ml-2 font-medium">{{ formatAmount(userBalance) }} {{ tokenSymbol }}</span>
              </div>
            </div>
          </div>

          <!-- Distribution Amount -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Amount per Distribution
            </label>
            <div class="flex">
              <input 
                v-model="distributionAmount"
                type="number"
                step="any"
                min="0"
                placeholder="0.0"
                class="flex-1 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                style="padding: 5px 10px;"
              />
              <div class="bg-gray-100 border border-l-0 border-gray-300 rounded-r-lg px-3 py-2 text-gray-600">
                {{ tokenSymbol || 'TOKEN' }}
              </div>
            </div>
            <p class="text-gray-500 text-sm mt-1">
              Amount of tokens each user will receive per claim
            </p>
          </div>

          <!-- Total Distributions -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Total Number of Distributions
            </label>
            <input 
              v-model="totalDistributions"
              type="number"
              min="1"
              placeholder="100"
              class="w-full border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              style="padding: 5px 10px;"
            />
            <p class="text-gray-500 text-sm mt-1">
              Total number of times tokens can be claimed from this faucet
            </p>
          </div>

          <!-- Total Tokens Needed -->
          <div v-if="totalTokensNeeded > 0" class="bg-blue-50 rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-2">Registration Summary</h3>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span>Amount per distribution:</span>
                <span class="font-medium">{{ distributionAmount }} {{ tokenSymbol }}</span>
              </div>
              <div class="flex justify-between">
                <span>Number of distributions:</span>
                <span class="font-medium">{{ totalDistributions }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-semibold">Your current allowance:</span>
                <span class="font-bold">{{ formatAmount(allowance) }} {{ tokenSymbol }}</span>
              </div>
              <div class="flex justify-between border-t pt-2">
                <span class="font-semibold">Total tokens needed:</span>
                <span class="font-bold">{{ formatAmount(totalTokensNeeded) }} {{ tokenSymbol }}</span>
              </div>
            </div>
          </div>

          <!-- Error Messages -->
          <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {{ error }}
          </div>

          <!-- Success Message -->
          <div v-if="success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {{ success }}
          </div>

          <!-- Balance Check -->
          <div v-if="hasInsufficientBalance && totalTokensNeeded > 0" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            Insufficient balance. You need {{ formatAmount(totalTokensNeeded) }} {{ tokenSymbol }} but only have {{ formatAmount(userBalance) }} {{ tokenSymbol }}.
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-center space-x-4">
            <!-- Approve Button -->
            <button 
              v-if="needsApproval && canRegister"
              @click="handleApprove"
              :disabled="isProcessing"
              class="bg-yellow-600 text-white hover:bg-yellow-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              style="padding: 5px 10px; border-radius: 20px;"
            >
              <span v-if="isApprovePending || isApproveLoading">Approving...</span>
              <span v-else>Approve {{ formatAmount(totalTokensNeeded) }} {{ tokenSymbol }}</span>
            </button>

            <!-- Register Button -->
            <button 
              @click="handleRegister"
              :disabled="!canRegister || needsApproval"
              class="bg-blue-600 text-white hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              style="padding: 5px 10px; border-radius: 20px;"
              :class="{ 'opacity-50': needsApproval }"
            >
              <span v-if="isRegisterPending || isRegisterLoading">Registering...</span>
              <span v-else-if="needsApproval">Approve Tokens First</span>
              <span v-else>Register Faucet</span>
            </button>
          </div>

          <div class="text-sm text-gray-600 bg-gray-50 rounded-lg p-4">
            <h4 class="font-semibold mb-2">Registration Process:</h4>
            <ol class="list-decimal list-inside space-y-1">
              <li>Enter the ERC20 token contract address</li>
              <li>Set the amount per distribution and total distributions</li>
              <li>Approve the faucet contract to spend your tokens</li>
              <li>Register the faucet (tokens will be transferred to the contract)</li>
            </ol>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>