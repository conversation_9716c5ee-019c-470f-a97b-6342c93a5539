@import "tailwindcss";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 小清新主题样式 */
:root {
  --primary-color: #7dd3fc;
  --primary-hover: #38bdf8;
  --secondary-color: #fbbf24;
  --success-color: #34d399;
  --danger-color: #f87171;
  --warning-color: #fbbf24;
  --info-color: #60a5fa;
  
  --bg-primary: #f0fdff;
  --bg-secondary: #ffffff;
  --bg-accent: #e0f2fe;
  
  --text-primary: #374151;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --border-radius: 12px;
  --border-radius-sm: 8px;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, var(--bg-primary) 0%, #ffffff 100%);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-accent);
  border-color: var(--primary-color);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #ef4444);
  color: white;
}

.btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn-rounded {
  border-radius: 20px;
}

/* 卡片样式 */
.card {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-accent), var(--bg-secondary));
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-light);
  background: var(--bg-accent);
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: var(--bg-secondary);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(125, 211, 252, 0.1);
}

/* 头部样式 */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-soft);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.header-nav {
  display: flex;
  gap: 0.5rem;
}

.nav-link {
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-sm);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background: var(--bg-accent);
  color: var(--text-primary);
}

.nav-link.active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
}

/* 页脚样式 */
.footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
  margin-top: auto;
}

/* 状态样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-success {
  background: rgba(52, 211, 153, 0.1);
  color: #059669;
}

.status-warning {
  background: rgba(251, 191, 36, 0.1);
  color: #d97706;
}

.status-danger {
  background: rgba(248, 113, 113, 0.1);
  color: #dc2626;
}

.status-info {
  background: rgba(96, 165, 250, 0.1);
  color: #2563eb;
}

/* 加载动画 */
.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }
  
  .header-nav {
    display: none;
  }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.rounded { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius); }

.shadow { box-shadow: var(--shadow-soft); }
.shadow-lg { box-shadow: var(--shadow-medium); }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-6xl { max-width: 72rem; }
