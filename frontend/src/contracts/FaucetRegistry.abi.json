[{"inputs": [{"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "contributor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "additionalDistributions", "type": "uint256"}], "name": "ContributionMade", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "blacklisted", "type": "bool"}], "name": "FaucetBlacklisted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "registrant", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "distributionAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalDistributions", "type": "uint256"}], "name": "FaucetRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "RegistrationToggled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensDistributed", "type": "event"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "blacklistedFaucets", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "claimRecords", "outputs": [{"internalType": "bool", "name": "hasClaimed", "type": "bool"}, {"internalType": "uint256", "name": "claimTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "name": "claimTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "additionalDistributions", "type": "uint256"}], "name": "contributeToFaucet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}], "name": "distributeTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "faucetTokens", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "faucets", "outputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "distributionAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalDistributions", "type": "uint256"}, {"internalType": "uint256", "name": "remainingDistributions", "type": "uint256"}, {"internalType": "address", "name": "registrant", "type": "address"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "registrationTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offset", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getActiveFaucetsPaginated", "outputs": [{"components": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "distributionAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalDistributions", "type": "uint256"}, {"internalType": "uint256", "name": "remainingDistributions", "type": "uint256"}, {"internalType": "address", "name": "registrant", "type": "address"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "registrationTime", "type": "uint256"}], "internalType": "struct FaucetRegistry.FaucetInfo[]", "name": "faucetInfos", "type": "tuple[]"}, {"internalType": "uint256", "name": "total", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "name": "getFaucetInfo", "outputs": [{"components": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "distributionAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalDistributions", "type": "uint256"}, {"internalType": "uint256", "name": "remainingDistributions", "type": "uint256"}, {"internalType": "address", "name": "registrant", "type": "address"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "registrationTime", "type": "uint256"}], "internalType": "struct FaucetRegistry.FaucetInfo", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offset", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getFaucetsPaginated", "outputs": [{"components": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "distributionAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalDistributions", "type": "uint256"}, {"internalType": "uint256", "name": "remainingDistributions", "type": "uint256"}, {"internalType": "address", "name": "registrant", "type": "address"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "registrationTime", "type": "uint256"}], "internalType": "struct FaucetRegistry.FaucetInfo[]", "name": "faucetInfos", "type": "tuple[]"}, {"internalType": "uint256", "name": "total", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}], "name": "hasUserClaimed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "name": "isFaucetRegistered", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "distributionAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalDistributions", "type": "uint256"}], "name": "registerFaucet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "registrationEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "bool", "name": "blacklisted", "type": "bool"}], "name": "setFaucetBlacklist", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "toggleRegistration", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalFaucets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]