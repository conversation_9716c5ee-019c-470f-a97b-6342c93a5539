[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "target", "type": "address"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "bool", "name": "success", "type": "bool"}], "name": "ContractCall", "type": "event"}, {"inputs": [{"internalType": "bytes[]", "name": "data", "type": "bytes[]"}], "name": "multicall", "outputs": [{"internalType": "bytes[]", "name": "results", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "targets", "type": "address[]"}, {"internalType": "bytes[]", "name": "data", "type": "bytes[]"}], "name": "multiCall", "outputs": [{"internalType": "bytes[]", "name": "results", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "caller", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "callsCount", "type": "uint256"}], "name": "MulticallExecuted", "type": "event"}, {"inputs": [{"internalType": "address[]", "name": "targets", "type": "address[]"}, {"internalType": "bytes[]", "name": "data", "type": "bytes[]"}, {"internalType": "bool[]", "name": "requireSuccess", "type": "bool[]"}], "name": "multiCallWithResults", "outputs": [{"internalType": "bytes[]", "name": "results", "type": "bytes[]"}, {"internalType": "bool[]", "name": "successes", "type": "bool[]"}], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "fallback"}, {"stateMutability": "payable", "type": "receive"}, {"inputs": [{"internalType": "address[]", "name": "targets", "type": "address[]"}, {"internalType": "bytes[]", "name": "data", "type": "bytes[]"}], "name": "multiStaticCall", "outputs": [{"internalType": "bytes[]", "name": "results", "type": "bytes[]"}], "stateMutability": "view", "type": "function"}]