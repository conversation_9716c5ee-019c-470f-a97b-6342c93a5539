import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/faucets'
    },
    {
      path: '/faucets',
      name: 'faucets',
      component: () => import('../views/FaucetListView.vue')
    },
    {
      path: '/faucet/:address',
      name: 'faucet-detail',
      component: () => import('../views/FaucetDetailView.vue'),
      props: true
    },
    {
      path: '/register',
      name: 'register-faucet',
      component: () => import('../views/RegisterFaucetView.vue')
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/AdminView.vue')
    },
  ]
})

export default router
