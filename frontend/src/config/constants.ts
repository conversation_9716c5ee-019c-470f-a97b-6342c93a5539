// This file contains constant values used throughout the frontend application.

/**
 * The address of the JovayMulticall contract deployed on the Jovay Testnet.
 * This contract provides efficient batch operations for multiple contract calls.
 * 
 * The abi of this contract is available in the frontend/src/contracts/JovayMulticall.abi.json file.
 * 
 * Usage:
 * - Use multiStaticCall() for batch read operations (view functions)
 * - Use multiCall() for batch write operations
 * - Use multiCallWithResults() when some calls are allowed to fail
 * 
 * Note: A pre-deployed JovayMulticall contract is available at the address below.
 * This shared instance can be used by all DApps and generally doesn't need redeployment.
 * However, if you have specific requirements or need custom functionality,
 * you can deploy your own instance using the contract in contracts/JovayMulticall.sol
 * 
 * Pre-deployed contract features:
 * - Gas-optimized batch operations
 * - Secure design that rejects direct ETH transfers
 * - Compatible with all EVM-compatible contracts
 * - No special permissions required for usage
 */
export const multicallContractAddress = '******************************************' as const;

/**
 * The address of the FaucetRegistry smart contract deployed on the Jovay Testnet.
 * This contract manages ERC20 token faucets for the DApp.
 */
export const faucetRegistryAddress: string = '******************************************';

/**
 * Placeholder address used to check if contract is deployed
 */
export const ZERO_ADDRESS = '******************************************';
