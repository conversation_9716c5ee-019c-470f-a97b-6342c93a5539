/**
 * Multicall utilities for batch contract queries
 * Optimizes performance by reducing multiple contract calls to a single multicall
 */

import { createPublicClient, http, encodeFunctionData, decodeFunctionResult } from 'viem'
import { multicallContractAddress } from '../config/constants'
import { jovayTestnet } from '../wagmi'
import importedJovayMulticallContractAbi from '../contracts/JovayMulticall.abi.json';

// ERC20 ABI for token info queries
const erc20Abi = [
  {
    "constant": true,
    "inputs": [],
    "name": "name",
    "outputs": [{"name": "", "type": "string"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "symbol",
    "outputs": [{"name": "", "type": "string"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "decimals",
    "outputs": [{"name": "", "type": "uint8"}],
    "type": "function"
  }
] as const

// Multicall ABI
const multicallAbi = importedJovayMulticallContractAbi

// Create public client for multicall operations
const publicClient = createPublicClient({
  chain: jovayTestnet,
  transport: http()
})

export interface TokenInfo {
  address: string
  name: string
  symbol: string
  decimals: number
}

/**
 * Batch fetch token names and symbols for multiple tokens using multicall
 * @param tokenAddresses Array of token contract addresses
 * @returns Promise<TokenInfo[]> Array of token info objects
 */
export async function batchGetTokenInfo(tokenAddresses: string[]): Promise<TokenInfo[]> {
  if (!tokenAddresses || tokenAddresses.length === 0) {
    return []
  }

  try {
    console.log(`🔄 Fetching token info for ${tokenAddresses.length} tokens using multicall...`)
    
    // Prepare multicall data - for each token we need name and symbol
    const targets: string[] = []
    const callData: string[] = []
    
    tokenAddresses.forEach(tokenAddress => {
      // name() call
      targets.push(tokenAddress)
      callData.push(encodeFunctionData({
        abi: erc20Abi,
        functionName: 'name'
      }))
      
      // symbol() call
      targets.push(tokenAddress)
      callData.push(encodeFunctionData({
        abi: erc20Abi,
        functionName: 'symbol'
      }))
    })

    // Execute multicall
    const results = await publicClient.readContract({
      address: multicallContractAddress as `0x${string}`,
      abi: multicallAbi,
      functionName: 'multiStaticCall',
      args: [targets as `0x${string}`[], callData as `0x${string}`[]]
    }) as `0x${string}`[]

    // Process results - every 2 results correspond to one token (name, symbol)
    const tokenInfos: TokenInfo[] = []
    for (let i = 0; i < tokenAddresses.length; i++) {
      const tokenAddress = tokenAddresses[i]
      const baseIndex = i * 2
      
      try {
        // Decode results for this token
        const name = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'name',
          data: results[baseIndex]
        }) as string
        
        const symbol = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'symbol',
          data: results[baseIndex + 1]
        }) as string

        tokenInfos.push({
          address: tokenAddress,
          name: name || 'Unknown Token',
          symbol: symbol || 'UNKNOWN',
          decimals: 18 // Default decimals, can be fetched separately if needed
        })
      } catch (decodeError) {
        console.warn(`Failed to decode token info for ${tokenAddress}:`, decodeError)
        
        // Add fallback data for failed tokens
        tokenInfos.push({
          address: tokenAddress,
          name: 'Unknown Token',
          symbol: 'UNKNOWN',
          decimals: 18
        })
      }
    }

    console.log(`✅ Successfully fetched info for ${tokenInfos.length} tokens via multicall`)
    return tokenInfos
  } catch (error) {
    console.error('❌ Multicall failed, falling back to individual calls:', error)
    
    // Fallback to individual calls if multicall fails
    return await batchGetTokenInfoFallback(tokenAddresses)
  }
}

/**
 * Fallback function for individual token info queries
 * @param tokenAddresses Array of token contract addresses
 * @returns Promise<TokenInfo[]> Array of token info objects
 */
async function batchGetTokenInfoFallback(tokenAddresses: string[]): Promise<TokenInfo[]> {
  console.log(`🔄 Using fallback individual calls for ${tokenAddresses.length} tokens...`)
  
  return await Promise.all(
    tokenAddresses.map(async (tokenAddress) => {
      try {
        const [name, symbol] = await Promise.all([
          publicClient.readContract({
            address: tokenAddress as `0x${string}`,
            abi: erc20Abi,
            functionName: 'name'
          }) as Promise<string>,
          publicClient.readContract({
            address: tokenAddress as `0x${string}`,
            abi: erc20Abi,
            functionName: 'symbol'
          }) as Promise<string>
        ])

        return {
          address: tokenAddress,
          name: name || 'Unknown Token',
          symbol: symbol || 'UNKNOWN',
          decimals: 18
        }
      } catch (error) {
        console.warn(`Failed to fetch token info for ${tokenAddress}:`, error)
        
        return {
          address: tokenAddress,
          name: 'Unknown Token',
          symbol: 'UNKNOWN',
          decimals: 18
        }
      }
    })
  )
}

/**
 * Batch fetch only token names for multiple tokens
 * @param tokenAddresses Array of token contract addresses
 * @returns Promise<string[]> Array of token names
 */
export async function batchGetTokenNames(tokenAddresses: string[]): Promise<string[]> {
  const tokenInfos = await batchGetTokenInfo(tokenAddresses)
  return tokenInfos.map(info => info.name)
}

/**
 * Batch fetch only token symbols for multiple tokens
 * @param tokenAddresses Array of token contract addresses
 * @returns Promise<string[]> Array of token symbols
 */
export async function batchGetTokenSymbols(tokenAddresses: string[]): Promise<string[]> {
  const tokenInfos = await batchGetTokenInfo(tokenAddresses)
  return tokenInfos.map(info => info.symbol)
}

/**
 * Check if multicall contract is available
 * @returns Promise<boolean> True if multicall contract is deployed and working
 */
export async function isMulticallAvailable(): Promise<boolean> {
  try {
    // Try a simple multicall with no operations
    await publicClient.readContract({
      address: multicallContractAddress as `0x${string}`,
      abi: multicallAbi,
      functionName: 'multiStaticCall',
      args: [[], []]
    })
    
    return true
  } catch (error) {
    console.warn('Multicall contract not available:', error)
    return false
  }
}
