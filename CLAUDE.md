# Claude Workspace

This file is for <PERSON> to understand the project context.

## Project Overview

This is a DApp template project designed to facilitate rapid development of decentralized applications on the Jovay network. It includes a basic frontend built with Vue 3, Wagmi, Pinia, and Tailwind CSS, and a simplified smart contract structure.

## Key Directories and Files

- `contracts/`: Contains sample Solidity smart contracts and their ABIs. This is where compiled contract ABIs should be placed.
- `frontend/`: The main frontend application, built with Vue 3.
  - `frontend/src/views/`: Vue components representing different pages/views of the DApp.
  - `frontend/src/stores/`: Pinia stores for state management.
  - `frontend/src/wagmi.ts`: Wagmi configuration, including chain definitions and contract ABIs.
  - `frontend/src/config/constants.ts`: Configuration file for contract addresses and other constants.
- `docs/`: Documentation for the project, including development guides and LLM usage instructions.
  - `docs/development-guide.md`: Instructions for setting up and running the project.
  - `docs/llm-usage-guide.md`: Guide for Large Language Models on how to use this template for DApp development.
  - `docs/template-requirements.md`: High-level requirements for a DApp built from this template.

## LLM/Agent Interaction Guidelines

When working with this project, consider the following:

- **Understand the Goal:** The primary goal of this template is to serve as a starting point for new DApp development, especially for automated generation by LLMs.
- **Make sure to plan the contract structure before starting to write the code.** The contract must satisfy the requirements of the DApp. If it's needed, using proxy or factory pattern is recommended(not use in simple ERC20 or ERC721). Use hardhat to manage the contract.
- **Adhere to Conventions:** Follow the existing code style, structure, and framework choices (Vue 3, Wagmi, Pinia, Tailwind CSS).
- **Contract ABIs:** Remember that contract compilation and deployment are external to this project. Focus on integrating pre-compiled ABIs into the frontend.
- **Modularity:** Aim for modular and reusable components and functions.
- **Documentation:** Keep the `docs/` up-to-date with any significant changes or additions.
- **Tools:** Some useful scripts in `tools/`
