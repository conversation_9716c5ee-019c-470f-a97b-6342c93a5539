/**
 * Usage: node tools/gen_new_addr.mjs
 * This command-line tool generates a new random Ethereum private key
 *  and its corresponding public address. Both are outputted in hexadecimal format.
 */
import { generatePrivateKey, privateKeyToAccount } from 'viem/accounts';

const privateKey = generatePrivateKey();
const account = privateKeyToAccount(privateKey);

console.log("Private Key:", privateKey);
console.log("Address:", account.address);
