# DApp Development Guide

This guide provides instructions for setting up and developing the DApp.

## Prerequisites

- Node.js and npm
- A web browser with a wallet extension (e.g., MetaMask)

## Getting Started

1.  **Install Dependencies:**
    ```bash
    cd frontend
    npm install
    ```

2.  **Run the Development Server:**
    ```bash
    npm run dev
    ```

3.  **Connect Your Wallet:**
    - Open the DApp in your browser.
    - Click the "Connect Wallet" button and approve the connection in your wallet extension.

## Smart Contracts

- The sample smart contracts are located in the `contracts` directory.
- **Important:** The compilation and deployment of smart contracts are outside the scope of this template project. Users are expected to compile their Solidity contracts using tools like Hardhat or Foundry, deploy them to the desired network, and then copy the generated ABI (Application Binary Interface) files into the `contracts` directory of this project. These ABI files are crucial for the frontend to interact with your deployed smart contracts.
- The ABIs for the contracts are also in the `contracts` directory and are imported into the frontend for contract interaction.

### Deploying Contracts to Jovay Testnet

To deploy contracts to the Jovay Testnet, ensure you have `DEPLOYER_PRIVATE_KEY` and `JOVAY_TESTNET_RPC_URL` set in your `.env` file. Then, you can use Hardhat to deploy your contracts:

```bash
npx hardhat run scripts/deploy.js --network jovay_testnet
```

**Note:** You will need to create a `scripts/deploy.js` file with your deployment logic.
