# Tools Documentation

This document describes the utility scripts available in the `tools/` directory.

## `tools/gen_new_addr.mjs`

This command-line tool generates a new random Ethereum private key and its corresponding public address. Both are outputted in hexadecimal format.

### Usage

To run the tool, execute the following command from the project root directory:

```bash
npm install # (if you haven't already)
node tools/gen_new_addr.mjs
```

### Example Output

```
Private Key: 0x...
Address: 0x...
```

**Note:** Keep your private keys secure. Do not share them or expose them in public repositories.

## `tools/compile_contracts.sh`

This script compiles the smart contracts in the `contracts/` directory using Hardhat.

### Usage

To run the script, execute the following command from the project root directory:

```bash
bash tools/compile_contracts.sh
```

## `tools/deploy_contracts.sh`

This script deploys the compiled smart contracts to the Jovay Testnet using Hardhat. Ensure your `.env` file is configured with `DEPLOYER_PRIVATE_KEY` and `JOVAY_TESTNET_RPC_URL`.

### Usage

To run the script, execute the following command from the project root directory:

```bash
bash tools/deploy_contracts.sh
```
