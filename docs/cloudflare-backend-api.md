# Cloudflare Pages Backend API Documentation

This document describes the backend API service implemented in `_worker.js` for the ERC20 Faucet DApp, which runs on Cloudflare Pages.

## Overview

The backend service provides server-side claim functionality with IP-based rate limiting to prevent abuse. Instead of users directly calling the smart contract, they request tokens through the backend API, which then distributes tokens on their behalf using admin privileges.

## API Endpoints

### POST /api/claim

Claims tokens from a specific ERC20 faucet on behalf of a user.

**Request Body:**
```json
{
  "tokenAddress": "0x...",
  "userAddress": "0x..."
}
```

**Response (Success):**
```json
{
  "success": true,
  "transactionHash": "0x...",
  "message": "Tokens claimed successfully!",
  "distributionAmount": "1000000000000000000"
}
```

**Response (Error):**
```json
{
  "success": false,
  "error": "Error message",
  "remainingTime": 45
}
```

**Rate Limiting:**
- 1 claim per IP address per minute
- Returns remaining time in seconds if rate limited

### GET /api/rate-limit

Checks the current rate limit status for the requesting IP.

**Response:**
```json
{
  "canClaim": true,
  "remainingTime": 0
}
```

### GET /api/health

Health check endpoint for monitoring.

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-01-23T10:30:00.000Z",
  "rateLimitEntries": 5
}
```

## Rate Limiting Implementation

### IP-Based Rate Limiting
- Uses Cloudflare's IP detection headers (`CF-Connecting-IP`, `X-Real-IP`, `X-Forwarded-For`)
- Stores claim timestamps in memory using a Map
- Automatically cleans up old entries after 1 minute
- Enforces 1 claim per IP per minute across all faucets

### Rate Limit Storage
The rate limiting uses in-memory storage with automatic cleanup:

```javascript
const rateLimitStore = new Map(); // IP -> last claim timestamp

function checkRateLimit(ip) {
  const now = Date.now();
  const lastClaim = rateLimitStore.get(ip);
  
  if (lastClaim && (now - lastClaim) < 60000) {
    return {
      allowed: false,
      remainingTime: Math.ceil((60000 - (now - lastClaim)) / 1000)
    };
  }
  
  rateLimitStore.set(ip, now);
  return { allowed: true, remainingTime: 0 };
}
```

## Smart Contract Integration

### Using Admin Privileges
The backend uses the `distributeTokens` function instead of `claimTokens`:
- `claimTokens`: User calls directly (msg.sender restriction)
- `distributeTokens`: Admin calls on behalf of user (owner only)

### Contract Validation
Before distributing tokens, the backend validates:
1. Faucet exists and is active
2. Tokens are available for distribution
3. User hasn't already claimed from this faucet
4. User address and token address are valid

### Transaction Flow
1. Client sends claim request to `/api/claim`
2. Backend validates request and checks rate limits
3. Backend reads faucet info from smart contract
4. Backend calls `distributeTokens(tokenAddress, userAddress)`
5. Backend waits for transaction confirmation
6. Backend returns success/failure response

## Environment Variables

### Required Environment Variables

Set these in Cloudflare Pages dashboard:

1. **FAUCET_REGISTRY_ADDRESS** (Environment Variable)
   ```
   FAUCET_REGISTRY_ADDRESS=0x6Eb9D235f8Ca31Ca63Fb99319F77050B63315770
   ```

2. **FAUCET_PRIVATE_KEY** (Secret - use wrangler secrets)
   ```bash
   wrangler secret put FAUCET_PRIVATE_KEY
   # Enter the private key of the admin account when prompted
   ```

### Security Notes
- Never commit private keys to version control
- Use Cloudflare's secret management for sensitive data
- The private key should belong to the contract owner/admin

## Deployment Instructions

### 1. Set Environment Variables
In Cloudflare Pages dashboard:
- Go to Settings → Environment Variables
- Add `FAUCET_REGISTRY_ADDRESS` with the deployed contract address

### 2. Set Private Key Secret
```bash
wrangler secret put FAUCET_PRIVATE_KEY
```
Enter the private key of the admin account (without 0x prefix).

### 3. Deploy to Cloudflare Pages
```bash
# Build frontend first
cd frontend && npm run build && cd ..

# Deploy to Cloudflare Pages
wrangler deploy
```

### 4. Verify Deployment
Test the health endpoint:
```bash
curl https://your-domain.pages.dev/api/health
```

## Error Handling

### Common Error Responses

**Rate Limited (429):**
```json
{
  "success": false,
  "error": "Rate limit exceeded. Please wait 45 seconds before claiming again.",
  "remainingTime": 45
}
```

**Invalid Request (400):**
```json
{
  "success": false,
  "error": "Invalid address format"
}
```

**Faucet Issues (400):**
```json
{
  "success": false,
  "error": "This faucet is not active"
}
```

**Server Error (500):**
```json
{
  "success": false,
  "error": "Internal server error: Transaction failed"
}
```

## CORS Configuration

The API includes CORS headers to allow frontend access:
```javascript
{
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization'
}
```

## Monitoring and Debugging

### Health Check
Use `/api/health` for monitoring:
- Returns current status
- Shows number of active rate limit entries
- Provides timestamp for last check

### Rate Limit Status
Use `/api/rate-limit` to check if an IP can claim:
- Non-destructive check (doesn't affect rate limit)
- Returns remaining time if rate limited

### Logs
Monitor Cloudflare Pages logs for:
- Transaction failures
- Rate limit violations
- Contract interaction errors

## Security Considerations

### Rate Limiting Benefits
- Prevents spam and abuse
- Distributes tokens fairly across users
- Reduces gas costs by batching through admin account

### IP-Based Limitations
- Users behind same NAT/proxy share rate limit
- VPN users can potentially bypass by changing IP
- Consider implementing additional rate limiting strategies

### Admin Key Security
- Store private key as Cloudflare secret
- Regularly rotate admin keys
- Monitor admin account balance and transactions

## Frontend Integration

The frontend automatically uses the backend API for claims:

```javascript
// Frontend claim function
const handleClaimTokens = async () => {
  const response = await fetch('/api/claim', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      tokenAddress: props.address,
      userAddress: userAddress.value
    })
  });
  
  const result = await response.json();
  // Handle response...
};
```

## Testing

### Local Testing
1. Set environment variables in `.env`
2. Run `wrangler dev` for local development
3. Test API endpoints with curl or Postman

### Production Testing
1. Deploy to Cloudflare Pages
2. Test rate limiting with multiple requests
3. Verify transaction confirmations on blockchain
4. Monitor error rates and response times

## Troubleshooting

### Common Issues

**"Server configuration error: Missing contract address or private key"**
- Solution: Set FAUCET_REGISTRY_ADDRESS and FAUCET_PRIVATE_KEY

**"Transaction failed"**
- Check admin account has sufficient ETH for gas
- Verify contract address is correct
- Ensure admin account is contract owner

**Rate limiting not working**
- Check Cloudflare IP headers are available
- Verify rate limit store is functioning
- Test with different IP addresses

**CORS errors**
- Ensure preflight OPTIONS requests are handled
- Verify CORS headers are set correctly
- Check frontend is making requests to correct domain