# LLM DApp Development Guide

This guide explains how a Large Language Model (LLM) can use this template to generate a complete Decentralized Application (DApp) based on user requirements.

## User Input

The LLM should expect the following inputs from the user:

1.  **Project Description:** A high-level description of the DApp's purpose and functionality. This should include specific details if an NFT factory deployment is required.
2.  **Frontend Pages:** A list of pages for the frontend, with a description of the components and user interactions on each page, especially noting pages related to NFT contract deployment.

## Development Process and Modification Steps

The LLM must strictly adhere to the following phased development process:

### 1. Requirements and Contract Design & Implementation Phase (LLM to Complete First)

Before starting any frontend code writing, the LLM must first complete the writing and design of the following documents and the core smart contract implementation:

*   **Requirements Document:**
    *   Based on the user's **Project Description** and **Frontend Pages** information, detail the DApp's functional requirements, user stories, and use cases. Pay special attention to the NFT factory deployment functionality, including parameters for deployment.
    *   **Output:** `docs/requirements.md` (or update `docs/template-requirements.md` to reflect new DApp-specific requirements).

*   **Code Structure Document:**
    *   Based on the DApp's complexity and module division, plan the overall code structure, including frontend and smart contract directory structure, module responsibilities, etc.
    *   **Output:** `docs/code-structure.md` (or update `docs/development-guide.md` to include new development steps and structure).

*   **Contract Design Document:**
    *   **Crucially, this document will detail the single Solidity contract responsible for all necessary functionality, including the NFT factory deployment interface.** It should explain the contract's functions, state variables, events, and how it will deploy new NFT contracts.
    *   **Output:** `docs/contract-design.md`.

*   **Solidity Contract Code Writing and Compilation:**
    *   **Implement the entire required functionality within a *single* Solidity contract file.** This contract must include a factory deployment interface that allows users to deploy new NFT contracts by providing corresponding parameters through the DApp frontend.
    *   Modify `contracts/*.sol` (or create a new, appropriately named single contract file) to implement the new contract logic.
    *   Compile the contract and generate its corresponding ABI file (e.g., `YourMainContract.abi`).
    *   **Output:** `jovay-contracts/YourMainContract.sol` and `jovay-contracts/YourMainContract.abi`.
    *   **Constraint:** Ensure all core smart contract logic, especially the NFT factory deployment, resides within this single contract file for simplicity and direct frontend interaction.
    *   **Deploy:** `scripts/deploy.js` should be updated to deploy the new contract. But the real deployment should be done by the hardhat or `tools/`.

### 2. Frontend Code Development Phase

Only after completing the above requirements and the *fully implemented and compiled* smart contract should the LLM proceed with frontend code development:

*   **Views:**
    *   Modify `frontend/src/views/Demo1View.vue` and `Demo2View.vue` files (or create new view files) to implement the required UI and functionality for each page. This includes designing input forms for NFT contract deployment parameters and displaying deployment results.
*   **State Management (Stores):**
    *   Update Pinia stores in `frontend/src/stores` to manage the application's state, including input parameters for contract deployment and handling contract interaction results.
*   **Wagmi Config:**
    *   Update `frontend/src/wagmi.ts` with the new contract ABI and address (of the single main contract). Ensure the configuration correctly points to the factory deployment function.
*   **Routing:**
    *   Modify routing in `frontend/src/App.vue` or a dedicated router file to reflect the new page structure, including any dedicated pages for NFT deployment.

## Subsequent Steps

After generating the code, the LLM should:

1.  **Install Dependencies:** Instruct the user to run `npm install -g wrangler` in the project directory, run `npm install` in the `frontend` directory.
2.  **Compile and Test:** Instruct the user on how to compile the smart contracts and run any tests.
3.  **Run the DApp:** Provide instructions to start the frontend development server.

## Available Tools

For information on command-line tools available in this project, refer to the [Tools Documentation](tools.md).
