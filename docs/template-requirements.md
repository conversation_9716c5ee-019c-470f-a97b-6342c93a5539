# DApp Template Requirements

This document outlines the high-level requirements for a DApp built from this template.

## Core Functionality

- **Wallet Integration:** Connect to standard Ethereum wallets (e.g., MetaMask) using Wagmi.
- **Smart Contract Interaction:** Read from and write to smart contracts deployed on the Jovay network.
- **Basic UI Components:** A set of simple, reusable Vue components for building the user interface.

## Pages

- **Demo1 Page:** A page demonstrating basic contract read operations.
- **Demo2 Page:** A page demonstrating contract write operations.
