Familiarize yourself with this DApp template project by reading the `README.md` and the `docs/` directory. 

Then, implement a DApp that:

我希望实现一个ERC20水龙头的DApp

0. 这是一个 Jovay Testnet上的DApp，前端默认可以直接访问 Jovay Testnet RPC Endpoint来在没有metamask登录的时候获取可申请的ERC20列表
1. 提供一个ERC20水龙头的登记合约，水龙头登记合约有个管理员，默认是部署人，管理员可以修改管理员地址
2. 水龙头登记合约的管理员可以禁用登记，以及将某个水龙头加入黑名单
3. 水龙头登记合约在登记的时候，申请人需要先授权ERC20足够额度给登记合约，然后在登记的时候填入ERC20合约地址，分发的次数，每次分发的数量，登记合约从用户地址里转移足够的token量到自身合约内，并记录下余额，已经分发次数等。如果已经禁用登记，或者授权金额不够转账，这个功能就会直接revert。登记完成 emit event
4. 水龙头登记合约提供获取所有ERC20水龙头的数量以及分页读取的功能，也可以单独查某个ERC20合约是否登记
5. 管理员可以调用水龙头登记合约的接口，用于分发指定ERC20代币给其他用户，登记合约内部负责管理已经分发次数等信息。分发完成后 emit event.
6. 水龙头合约中，每个用户地址对于每个ERC20代币只能申领一次，并且记录下申领记录和时间。并提供接口来查询申领记录。
7. 任何人都可以给已经登记的ERC20，额外贡献分发次数，每次分发的代币数量不变。贡献完成后emit event.
8. 水龙头登记界面提供申领页面，登记ERC20分发页面，后台管理页面。其中后台管理页面和ERC20分发页面需要提醒用户必须登录。申领页面没登录也可以请求 Jovay Testnet RPC endpoint来获取可申领的ERC20列表
9. 申领页面用卡片风格排布可申领的ERC20的信息，点击后进入具体的申领页面，具体的申领页面既可以申领代币，也可以贡献代币。这个申领页面展示ERC20信息，分发信息，以及申请按钮和贡献按钮
10. 如果点击贡献按钮，就可以输入贡献分发次数，然后自动计算好要贡献的代币数量，在没有足够approve金额的时候让用户approve，然后就让用户可以贡献代币到登记合约。
11. 后端服务使用cloudflare pages服务，逻辑写在 _worker.js 中，用户点击申领按钮后，请求先发送给 _worker.js 中定义的后端接口 /api/claim, 内部使用 viem 库来实现调用登记合约完成分发。 这个接口在内存变量中控制好申领频率，对于同一个原始IP，限制1分钟内最多申领1次代币
12. 整个DApp使用英文
