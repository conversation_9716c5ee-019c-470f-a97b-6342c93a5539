const { ethers } = require("hardhat");

// You need update this deploy script by your logic
async function main() {
  // Counter is the name of the contract in the artifacts, not the solidity file name
  const Counter = await ethers.getContractFactory("Counter");
  const demo1 = await Counter.deploy();
  await demo1.waitForDeployment();
  console.log(`Demo1 deployed to ${demo1.target}`);

  const Counter2 = await ethers.getContractFactory("Counter2");
  const demo2 = await Counter2.deploy();
  await demo2.waitForDeployment();
  console.log(`Demo2 deployed to ${demo2.target}`);
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
