const { ethers } = require("hardhat");

async function main() {
  const [deployer] = await ethers.getSigners();

  console.log("Deploying FaucetRegistry with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)));

  const FaucetRegistry = await ethers.getContractFactory("FaucetRegistry");
  const faucetRegistry = await FaucetRegistry.deploy(deployer.address);

  await faucetRegistry.waitForDeployment();

  const contractAddress = await faucetRegistry.getAddress();
  console.log("FaucetRegistry deployed to:", contractAddress);

  // Save deployment info
  const deploymentInfo = {
    network: "jovay_testnet",
    contractAddress: contractAddress,
    deployer: deployer.address,
    timestamp: new Date().toISOString()
  };

  console.log("Deployment Info:", JSON.stringify(deploymentInfo, null, 2));
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });