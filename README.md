# ERC20 Faucet DApp

A decentralized application for managing ERC20 token faucets on the Jovay Testnet. This DApp allows users to register ERC20 tokens as faucets, claim tokens from available faucets, and contribute additional tokens to existing faucets.

## Features

### For Users
- **Browse Faucets**: View all available ERC20 token faucets in a card-style layout
- **Claim Tokens**: Claim free ERC20 tokens from registered faucets (one claim per user per token)
- **Contribute to Faucets**: Add more tokens to existing faucets to increase available distributions
- **No Wallet Required for Browsing**: View available faucets without connecting a wallet

### For Faucet Registrants
- **Register New Faucets**: Register your ERC20 token as a faucet by depositing tokens
- **Set Distribution Parameters**: Configure the amount per claim and total number of distributions
- **Track Performance**: View remaining distributions and total claims for your faucets

### For Administrators
- **Admin Panel**: Complete administrative control over the faucet registry
- **Toggle Registration**: Enable or disable new faucet registrations
- **Blacklist Management**: Add or remove faucets from the blacklist
- **Manual Distribution**: Manually distribute tokens to specific recipients
- **Transfer Ownership**: Transfer admin rights to another address

## Smart Contract Features

### FaucetRegistry Contract
- **Admin Management**: Contract owner has full administrative control
- **Registration Control**: Can enable/disable new faucet registrations
- **Blacklist System**: Prevent specific faucets from being used
- **One-Time Claims**: Each user can only claim once per token
- **Contribution System**: Allow additional tokens to be added to existing faucets
- **Pagination Support**: Efficient retrieval of faucet lists with pagination
- **Event Logging**: All major actions emit events for transparency

## Architecture

### Smart Contracts
- **FaucetRegistry.sol**: Main contract managing all faucet operations
- **OpenZeppelin Integration**: Uses battle-tested contracts for safety (Ownable, ReentrancyGuard, SafeERC20)

### Frontend
- **Vue 3**: Modern reactive framework
- **Wagmi**: Ethereum interaction hooks
- **Viem**: Type-safe Ethereum client
- **Tailwind CSS**: Utility-first CSS framework
- **TypeScript**: Type safety throughout the application

## Getting Started

### Prerequisites
- Node.js and npm
- MetaMask or compatible wallet
- Access to Jovay Testnet

### Installation

1. **Install Dependencies**
   ```bash
   # Install root dependencies
   npm install
   
   # Install frontend dependencies
   cd frontend
   npm install
   cd ..
   ```

2. **Compile Smart Contracts**
   ```bash
   npx hardhat compile
   ```

3. **Deploy the FaucetRegistry Contract**
   ```bash
   # Set environment variables
   export DEPLOYER_ADDRESS="your_address_to_deploy"
   export DEPLOYER_PRIVATE_KEY="your_private_key"
   export JOVAY_TESTNET_RPC_URL="https://api.zan.top/public/jovay-testnet"
   
   # Deploy to Jovay Testnet
   npx hardhat run scripts/deploy-faucet.js --network jovay_testnet
   ```

4. **Update Contract Address**
   - Copy the deployed contract address
   - Update `frontend/src/config/constants.ts` with the deployed address:
   ```typescript
   export const faucetRegistryAddress = 'YOUR_DEPLOYED_CONTRACT_ADDRESS';
   ```

5. **Run the Frontend**
   ```bash
   cd frontend
   npm run dev
   ```

6. **Access the DApp**
   - Open your browser and navigate to `http://localhost:5173`
   - Connect your wallet to interact with the DApp

## Usage Guide

### Browsing Faucets
1. Visit the main page to see all available faucets
2. Each faucet card shows:
   - Token contract address
   - Amount per claim
   - Remaining distributions
   - Total distributions available

### Claiming Tokens
1. Click on any faucet card to view details
2. Connect your wallet if not already connected
3. Click "Claim Tokens" to receive the specified amount
4. Confirm the transaction in your wallet
5. Wait for confirmation - you can only claim once per token

### Contributing to Faucets
1. Navigate to a faucet's detail page
2. In the "Contribute to Faucet" section:
   - Enter the number of additional claims you want to add
   - The system will calculate the required token amount
   - Approve the token spend if necessary
   - Confirm the contribution transaction

### Registering a New Faucet
1. Navigate to the "Register" page
2. Connect your wallet
3. Enter the ERC20 token contract address
4. Set the distribution amount per claim
5. Set the total number of distributions
6. Approve the required token amount
7. Register the faucet

### Admin Functions
1. Navigate to the "Admin" page
2. Connect with the owner wallet
3. Available functions:
   - Toggle registration on/off
   - Manually distribute tokens
   - Blacklist/unblacklist faucets
   - Transfer ownership

## Network Configuration

### Jovay Testnet Details
- **Chain ID**: 2019775
- **RPC URL**: https://api.zan.top/public/jovay-testnet
- **Explorer**: https://sepolia-explorer.jovay.io/l2
- **Native Currency**: ETH

## Development

### Project Structure
```
├── contracts/              # Smart contracts and ABIs
├── frontend/               # Vue.js frontend application
│   ├── src/
│   │   ├── views/         # Vue components for pages
│   │   ├── config/        # Configuration files
│   │   ├── contracts/     # Contract ABIs for frontend
│   │   └── wagmi.ts       # Wagmi configuration
├── scripts/               # Deployment scripts
└── docs/                  # Documentation
```

### Available Scripts

#### Root Level
- `npm run compile`: Compile smart contracts
- `npm run deploy`: Deploy contracts to Jovay Testnet

#### Frontend
- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run type-check`: TypeScript type checking
- `npm run lint`: Lint code

### Testing

1. **Contract Testing**
   ```bash
   npx hardhat test
   ```

2. **Frontend Testing**
   ```bash
   cd frontend
   npm run type-check
   npm run build
   ```

## Security Considerations

- **ReentrancyGuard**: Prevents reentrancy attacks
- **SafeERC20**: Safe token transfers and approvals
- **Ownable**: Secure admin access control
- **Input Validation**: All user inputs are validated
- **One-Time Claims**: Prevents multiple claims by the same user

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For questions or issues:
1. Check the existing documentation
2. Review the smart contract code
3. Test on Jovay Testnet first
4. Report issues with detailed information

## Contract Addresses

- **Jovay Testnet**: Update after deployment

## Changelog

### v1.0.0
- Initial release
- Basic faucet functionality
- Admin panel
- Contribution system
- One-time claim restriction
- Blacklist management