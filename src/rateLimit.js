// In-memory rate limiting storage
const rateLimitStore = new Map();

// Rate limiting function
export function checkRateLimit(ip) {
  const now = Date.now();
  const lastClaim = rateLimitStore.get(ip);
  
  if (lastClaim && (now - lastClaim) < 60000) { // 1 minute = 60000ms
    return {
      allowed: false,
      remainingTime: Math.ceil((60000 - (now - lastClaim)) / 1000)
    };
  }
  
  rateLimitStore.set(ip, now);
  
  // Clean up old entries (older than 1 minute)
  for (const [key, value] of rateLimitStore.entries()) {
    if (now - value > 60000) {
      rateLimitStore.delete(key);
    }
  }
  
  return { allowed: true, remainingTime: 0 };
}

// Get rate limit status for an IP
export function getRateLimitStatus(ip) {
  const now = Date.now();
  const lastClaim = rateLimitStore.get(ip);
  
  if (lastClaim && (now - lastClaim) < 60000) {
    const remainingTime = Math.ceil((60000 - (now - lastClaim)) / 1000);
    return {
      canClaim: false,
      remainingTime
    };
  }
  
  return {
    canClaim: true,
    remainingTime: 0
  };
}

// Get the number of entries in rate limit store (for health check)
export function getRateLimitStoreSize() {
  return rateLimitStore.size;
}
