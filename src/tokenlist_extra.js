/**
 * Token List Extra Information
 * 
 * 这个文件定义了 token 的额外信息，主要用于增强 token list 的显示效果。
 * 
 * 主要用途：
 * 1. logoURI: 为 token 提供图标 URL，用于在 Uniswap 等 DeFi 应用中显示
 * 2. name/symbol: 提供人类可读的 token 名称和符号，主要用于显示和识别
 * 
 * 注意事项：
 * - address 必须是小写格式，用于匹配查找
 * - name 和 symbol 这里主要是给人看的，实际合约数据会覆盖这些值
 * - logoURI 是最重要的字段，会直接用于最终的 token list
 * - 如果合约中的 name/symbol 获取失败，会使用这里定义的作为备用
 */

// Token 额外信息配置
// 格式: address (小写) -> { name, symbol, logoURI }
export const tokenExtraInfo = {
  // 示例配置 - MyToken1
  '0xef6a7ae0efbe9b5ad9b720b551feca66893a8ad7': {
    name: 'MyToken1',           // 人类可读的 token 名称
    symbol: 'MTK1',             // 人类可读的 token 符号
    logoURI: ''
  },

  // 示例配置 - USD Test Token 1
  '0xa5d81b84c913a138cf4457100b07dc9f76a05ffb': {
    name: 'USD Test Token 1',   // 人类可读的 token 名称
    symbol: 'USD1',             // 人类可读的 token 符号
    logoURI: 'https://swap.swapsphere.space/images/usd1-logo.png'
  },

  // 示例配置 - USD Test Token 2
  '0xabfe6e96a1dc73d7ac97463c2b689d42e3494c7a': {
    name: 'USD Test Token 2',   // 人类可读的 token 名称
    symbol: 'USD2',             // 人类可读的 token 符号
    logoURI: 'https://swap.swapsphere.space/images/usd2-logo.png'
  },

  // 添加更多 token 信息的示例格式：
  // '0x另一个token地址的小写格式': {
  //   name: 'Token 显示名称',
  //   symbol: 'TOKEN',
  //   logoURI: 'https://example.com/token-logo.png'
  // },
};

/**
 * 根据 token 地址获取额外信息
 * @param {string} address - Token 合约地址
 * @returns {object|null} 额外信息对象或 null
 */
export function getTokenExtraInfo(address) {
  if (!address || typeof address !== 'string') {
    return null;
  }
  
  // 转换为小写进行匹配
  const lowerAddress = address.toLowerCase();
  return tokenExtraInfo[lowerAddress] || null;
}

/**
 * 获取 token 的 logo URI
 * @param {string} address - Token 合约地址
 * @returns {string} Logo URI 或空字符串
 */
export function getTokenLogoURI(address) {
  const extraInfo = getTokenExtraInfo(address);
  return extraInfo?.logoURI || '';
}

/**
 * 获取 token 的显示名称（备用）
 * @param {string} address - Token 合约地址
 * @returns {string|null} 显示名称或 null
 */
export function getTokenDisplayName(address) {
  const extraInfo = getTokenExtraInfo(address);
  return extraInfo?.name || null;
}

/**
 * 获取 token 的显示符号（备用）
 * @param {string} address - Token 合约地址
 * @returns {string|null} 显示符号或 null
 */
export function getTokenDisplaySymbol(address) {
  const extraInfo = getTokenExtraInfo(address);
  return extraInfo?.symbol || null;
}

/**
 * 检查是否有 token 的额外信息
 * @param {string} address - Token 合约地址
 * @returns {boolean} 是否存在额外信息
 */
export function hasTokenExtraInfo(address) {
  return getTokenExtraInfo(address) !== null;
}

/**
 * 获取所有已配置的 token 地址列表
 * @returns {string[]} Token 地址数组
 */
export function getAllConfiguredTokens() {
  return Object.keys(tokenExtraInfo);
}

/**
 * 添加或更新 token 额外信息（运行时）
 * @param {string} address - Token 合约地址
 * @param {object} info - Token 信息 { name, symbol, logoURI }
 */
export function setTokenExtraInfo(address, info) {
  if (!address || typeof address !== 'string') {
    throw new Error('Invalid token address');
  }
  
  const lowerAddress = address.toLowerCase();
  tokenExtraInfo[lowerAddress] = {
    name: info.name || '',
    symbol: info.symbol || '',
    logoURI: info.logoURI || ''
  };
}
