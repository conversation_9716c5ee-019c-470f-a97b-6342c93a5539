import { createPublicClient, http, encodeFunctionData, decodeFunctionResult } from 'viem';
import { jovayTestnet, faucetRegistryAbi, erc20Abi, faucetRegistryAddress, multicallContractAddress, multicallAbi } from './config.js';
import { getTokenLogoURI, getTokenDisplayName, getTokenDisplaySymbol } from './tokenlist_extra.js';

// Cache for contract query results
const cache = new Map();
const CACHE_DURATION = 3 * 60 * 1000; // 3 minutes in milliseconds

// Predefined token list (whitelist) - can be configured as needed
const predefinedTokens = [
  // Example predefined tokens - currently empty as requested
  // {
  //   chainId: 2019775,
  //   address: '0x...',
  //   decimals: 18,
  //   symbol: 'TOKEN',
  //   name: 'Token Name',
  //   logoURI: 'https://...'
  // }
];

// Create public client for blockchain queries
function createClient() {
  return createPublicClient({
    chain: jovayTestnet,
    transport: http()
  });
}

// Get token information from contract using multicall for batch operations
async function getTokenInfoBatch(publicClient, tokenAddresses) {
  if (tokenAddresses.length === 0) {
    return [];
  }

  try {
    // Prepare multicall data for name, symbol, and decimals for each token
    const targets = [];
    const callData = [];

    // For each token, we need 3 calls: name, symbol, decimals
    tokenAddresses.forEach(tokenAddress => {
      // name() call
      targets.push(tokenAddress);
      callData.push(encodeFunctionData({
        abi: erc20Abi,
        functionName: 'name'
      }));

      // symbol() call
      targets.push(tokenAddress);
      callData.push(encodeFunctionData({
        abi: erc20Abi,
        functionName: 'symbol'
      }));

      // decimals() call
      targets.push(tokenAddress);
      callData.push(encodeFunctionData({
        abi: erc20Abi,
        functionName: 'decimals'
      }));
    });

    // Execute multicall
    const results = await publicClient.readContract({
      address: multicallContractAddress,
      abi: multicallAbi,
      functionName: 'multiStaticCall',
      args: [targets, callData]
    });

    // Process results - every 3 results correspond to one token (name, symbol, decimals)
    const tokenInfos = [];
    for (let i = 0; i < tokenAddresses.length; i++) {
      const tokenAddress = tokenAddresses[i];
      const baseIndex = i * 3;

      try {
        // Decode results for this token
        const name = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'name',
          data: results[baseIndex]
        });

        const symbol = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'symbol',
          data: results[baseIndex + 1]
        });

        const decimals = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'decimals',
          data: results[baseIndex + 2]
        });

        // 获取额外信息作为备用
        const displayName = getTokenDisplayName(tokenAddress);
        const displaySymbol = getTokenDisplaySymbol(tokenAddress);

        tokenInfos.push({
          address: tokenAddress,
          name: name || displayName || 'Unknown Token',
          symbol: symbol || displaySymbol || 'UNKNOWN',
          decimals: Number(decimals) || 18
        });
      } catch (decodeError) {
        console.warn(`Failed to decode token info for ${tokenAddress}:`, decodeError);

        // 如果解码失败，使用额外信息作为备用
        const displayName = getTokenDisplayName(tokenAddress);
        const displaySymbol = getTokenDisplaySymbol(tokenAddress);

        tokenInfos.push({
          address: tokenAddress,
          name: displayName || 'Unknown Token',
          symbol: displaySymbol || 'UNKNOWN',
          decimals: 18
        });
      }
    }

    return tokenInfos;
  } catch (error) {
    console.warn('Multicall failed, falling back to individual calls:', error);

    // Fallback to individual calls if multicall fails
    return await Promise.all(
      tokenAddresses.map(async (tokenAddress) => {
        return await getTokenInfoFallback(publicClient, tokenAddress);
      })
    );
  }
}

// Fallback function for individual token info queries
async function getTokenInfoFallback(publicClient, tokenAddress) {
  try {
    const [name, symbol, decimals] = await Promise.all([
      publicClient.readContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'name'
      }),
      publicClient.readContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'symbol'
      }),
      publicClient.readContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'decimals'
      })
    ]);

    // 获取额外信息作为备用
    const displayName = getTokenDisplayName(tokenAddress);
    const displaySymbol = getTokenDisplaySymbol(tokenAddress);

    return {
      address: tokenAddress,
      name: name || displayName || 'Unknown Token',
      symbol: symbol || displaySymbol || 'UNKNOWN',
      decimals: Number(decimals) || 18
    };
  } catch (error) {
    console.warn(`Failed to fetch token info for ${tokenAddress}:`, error);

    // 如果合约查询失败，尝试使用额外信息作为备用
    const displayName = getTokenDisplayName(tokenAddress);
    const displaySymbol = getTokenDisplaySymbol(tokenAddress);

    return {
      address: tokenAddress,
      name: displayName || 'Unknown Token',
      symbol: displaySymbol || 'UNKNOWN',
      decimals: 18
    };
  }
}

// Get active faucets from contract with caching
async function getActiveFaucets() {
  const cacheKey = 'activeFaucets';
  const now = Date.now();
  
  // Check cache first
  const cached = cache.get(cacheKey);
  if (cached && (now - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }

  try {
    const publicClient = createClient();
    
    // Call getActiveFaucetsPaginated with offset=0, limit=100
    const result = await publicClient.readContract({
      address: faucetRegistryAddress,
      abi: faucetRegistryAbi,
      functionName: 'getActiveFaucetsPaginated',
      args: [0n, 100n]
    });

    const [faucetInfos, total] = result;
    
    // Cache the result
    cache.set(cacheKey, {
      data: faucetInfos,
      timestamp: now
    });

    return faucetInfos;
  } catch (error) {
    console.error('Failed to fetch active faucets:', error);
    
    // Return cached data if available, even if expired
    const cached = cache.get(cacheKey);
    if (cached) {
      return cached.data;
    }
    
    return [];
  }
}

// Generate token list for Uniswap
export async function generateTokenList() {
  try {
    const publicClient = createClient();
    
    // Get active faucets from contract
    const activeFaucets = await getActiveFaucets();
    
    // Extract unique token addresses
    const tokenAddresses = [...new Set(activeFaucets.map(faucet => faucet.tokenAddress))];
    
    // Get token information for all addresses using multicall (batch operation)
    console.log(`Fetching token info for ${tokenAddresses.length} tokens using multicall...`);
    const tokenInfos = await getTokenInfoBatch(publicClient, tokenAddresses);

    // Transform to final format with logo URIs
    const faucetTokens = tokenInfos.map(tokenInfo => {
      const logoURI = getTokenLogoURI(tokenInfo.address);

      return {
        chainId: jovayTestnet.id,
        address: tokenInfo.address,
        decimals: tokenInfo.decimals,
        symbol: tokenInfo.symbol,
        name: tokenInfo.name,
        logoURI: logoURI // 使用额外信息中定义的 logoURI
      };
    });

    // Combine predefined tokens with faucet tokens
    const allTokens = [...predefinedTokens, ...faucetTokens];
    
    // Remove duplicates based on address (case-insensitive)
    const uniqueTokens = allTokens.reduce((acc, token) => {
      const existingIndex = acc.findIndex(
        t => t.address.toLowerCase() === token.address.toLowerCase()
      );
      
      if (existingIndex === -1) {
        acc.push(token);
      } else {
        // If duplicate found, prefer predefined token over faucet token
        if (predefinedTokens.some(pt => pt.address.toLowerCase() === token.address.toLowerCase())) {
          acc[existingIndex] = token;
        }
      }
      
      return acc;
    }, []);

    // Sort tokens by symbol for consistent ordering
    uniqueTokens.sort((a, b) => a.symbol.localeCompare(b.symbol));

    return {
      name: 'Jovay Testnet Token List',
      timestamp: new Date().toISOString(),
      version: {
        major: 1,
        minor: 0,
        patch: 0
      },
      tags: {},
      logoURI: '',
      keywords: ['jovay', 'testnet', 'faucet'],
      tokens: uniqueTokens
    };
  } catch (error) {
    console.error('Error generating token list:', error);
    
    // Return minimal token list with predefined tokens only
    return {
      name: 'Jovay Testnet Token List',
      timestamp: new Date().toISOString(),
      version: {
        major: 1,
        minor: 0,
        patch: 0
      },
      tags: {},
      logoURI: '',
      keywords: ['jovay', 'testnet', 'faucet'],
      tokens: predefinedTokens
    };
  }
}

// Clear cache (useful for testing or manual refresh)
export function clearTokenListCache() {
  cache.clear();
}
