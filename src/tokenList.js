import { createPublicClient, http } from 'viem';
import { jovayTestnet, faucetRegistryAbi, erc20Abi, faucetRegistryAddress } from './config.js';

// Cache for contract query results
const cache = new Map();
const CACHE_DURATION = 3 * 60 * 1000; // 3 minutes in milliseconds

// Predefined token list (whitelist) - can be configured as needed
const predefinedTokens = [
  // Example predefined tokens - currently empty as requested
  // {
  //   chainId: 2019775,
  //   address: '0x...',
  //   decimals: 18,
  //   symbol: 'TOKEN',
  //   name: 'Token Name',
  //   logoURI: 'https://...'
  // }
];

// Create public client for blockchain queries
function createClient() {
  return createPublicClient({
    chain: jovayTestnet,
    transport: http()
  });
}

// Get token information from contract
async function getTokenInfo(publicClient, tokenAddress) {
  try {
    const [name, symbol, decimals] = await Promise.all([
      publicClient.readContract({
        address: tokenAddress,
        abi: erc20<PERSON>bi,
        functionName: 'name'
      }),
      publicClient.readContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'symbol'
      }),
      publicClient.readContract({
        address: tokenAddress,
        abi: erc20Abi,
        functionName: 'decimals'
      })
    ]);

    return {
      name: name || 'Unknown Token',
      symbol: symbol || 'UNKNOWN',
      decimals: Number(decimals) || 18
    };
  } catch (error) {
    console.warn(`Failed to fetch token info for ${tokenAddress}:`, error);
    return {
      name: 'Unknown Token',
      symbol: 'UNKNOWN',
      decimals: 18
    };
  }
}

// Get active faucets from contract with caching
async function getActiveFaucets() {
  const cacheKey = 'activeFaucets';
  const now = Date.now();
  
  // Check cache first
  const cached = cache.get(cacheKey);
  if (cached && (now - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }

  try {
    const publicClient = createClient();
    
    // Call getActiveFaucetsPaginated with offset=0, limit=100
    const result = await publicClient.readContract({
      address: faucetRegistryAddress,
      abi: faucetRegistryAbi,
      functionName: 'getActiveFaucetsPaginated',
      args: [0n, 100n]
    });

    const [faucetInfos, total] = result;
    
    // Cache the result
    cache.set(cacheKey, {
      data: faucetInfos,
      timestamp: now
    });

    return faucetInfos;
  } catch (error) {
    console.error('Failed to fetch active faucets:', error);
    
    // Return cached data if available, even if expired
    const cached = cache.get(cacheKey);
    if (cached) {
      return cached.data;
    }
    
    return [];
  }
}

// Generate token list for Uniswap
export async function generateTokenList() {
  try {
    const publicClient = createClient();
    
    // Get active faucets from contract
    const activeFaucets = await getActiveFaucets();
    
    // Extract unique token addresses
    const tokenAddresses = [...new Set(activeFaucets.map(faucet => faucet.tokenAddress))];
    
    // Get token information for each address
    const faucetTokens = await Promise.all(
      tokenAddresses.map(async (address) => {
        const tokenInfo = await getTokenInfo(publicClient, address);
        return {
          chainId: jovayTestnet.id,
          address: address,
          decimals: tokenInfo.decimals,
          symbol: tokenInfo.symbol,
          name: tokenInfo.name,
          logoURI: '' // Empty for now, can be populated later if needed
        };
      })
    );

    // Combine predefined tokens with faucet tokens
    const allTokens = [...predefinedTokens, ...faucetTokens];
    
    // Remove duplicates based on address (case-insensitive)
    const uniqueTokens = allTokens.reduce((acc, token) => {
      const existingIndex = acc.findIndex(
        t => t.address.toLowerCase() === token.address.toLowerCase()
      );
      
      if (existingIndex === -1) {
        acc.push(token);
      } else {
        // If duplicate found, prefer predefined token over faucet token
        if (predefinedTokens.some(pt => pt.address.toLowerCase() === token.address.toLowerCase())) {
          acc[existingIndex] = token;
        }
      }
      
      return acc;
    }, []);

    // Sort tokens by symbol for consistent ordering
    uniqueTokens.sort((a, b) => a.symbol.localeCompare(b.symbol));

    return {
      name: 'Jovay Testnet Token List',
      timestamp: new Date().toISOString(),
      version: {
        major: 1,
        minor: 0,
        patch: 0
      },
      tags: {},
      logoURI: '',
      keywords: ['jovay', 'testnet', 'faucet'],
      tokens: uniqueTokens
    };
  } catch (error) {
    console.error('Error generating token list:', error);
    
    // Return minimal token list with predefined tokens only
    return {
      name: 'Jovay Testnet Token List',
      timestamp: new Date().toISOString(),
      version: {
        major: 1,
        minor: 0,
        patch: 0
      },
      tags: {},
      logoURI: '',
      keywords: ['jovay', 'testnet', 'faucet'],
      tokens: predefinedTokens
    };
  }
}

// Clear cache (useful for testing or manual refresh)
export function clearTokenListCache() {
  cache.clear();
}
