import { createPublicClient, createWalletClient, http } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { jovayTestnet, faucetRegistryAbi } from './config.js';

// Create blockchain clients
function createClients(privateKey) {
  const publicClient = createPublicClient({
    chain: jovayTestnet,
    transport: http()
  });
  
  const account = privateKeyToAccount(privateKey);
  const walletClient = createWalletClient({
    account,
    chain: jovayTestnet,
    transport: http()
  });
  
  return { publicClient, walletClient };
}

// Get faucet information
export async function getFaucetInfo(publicClient, faucetRegistryAddress, tokenAddress) {
  return await publicClient.readContract({
    address: faucetRegistryAddress,
    abi: faucetRegistryAbi,
    functionName: 'getFaucetInfo',
    args: [tokenAddress]
  });
}

// Check if user has already claimed tokens
export async function hasUserClaimed(publicClient, faucetRegistryAddress, userAddress, tokenAddress) {
  const [hasClaimed] = await publicClient.readContract({
    address: faucetRegistryAddress,
    abi: faucetRegistryAbi,
    functionName: 'hasUserClaimed',
    args: [userAddress, tokenAddress]
  });
  return hasClaimed;
}

// Execute token distribution
export async function distributeTokens(walletClient, faucetRegistryAddress, tokenAddress, userAddress) {
  return await walletClient.writeContract({
    address: faucetRegistryAddress,
    abi: faucetRegistryAbi,
    functionName: 'distributeTokens',
    args: [tokenAddress, userAddress]
  });
}

// Main claim tokens function
export async function claimTokens(env, tokenAddress, userAddress) {
  // Check environment variables
  if (!env.FAUCET_REGISTRY_ADDRESS) {
    throw new Error('Server configuration error: Missing faucet registry contract address');
  }
  
  if (!env.FAUCET_PRIVATE_KEY) {
    throw new Error('Server configuration error: Missing faucet admin private key');
  }
  
  // Create clients
  const { publicClient, walletClient } = createClients(env.FAUCET_PRIVATE_KEY);
  
  // Check if faucet exists and is active
  const faucetInfo = await getFaucetInfo(publicClient, env.FAUCET_REGISTRY_ADDRESS, tokenAddress);
  
  if (!faucetInfo.isActive) {
    throw new Error('This faucet is not active');
  }
  
  if (faucetInfo.remainingDistributions === 0n) {
    throw new Error('No more tokens available in this faucet');
  }
  
  // Check if user has already claimed
  const hasClaimed = await hasUserClaimed(publicClient, env.FAUCET_REGISTRY_ADDRESS, userAddress, tokenAddress);
  
  if (hasClaimed) {
    throw new Error('You have already claimed tokens from this faucet');
  }
  
  // Execute the distribution transaction
  const hash = await distributeTokens(walletClient, env.FAUCET_REGISTRY_ADDRESS, tokenAddress, userAddress);
  
  // Wait for transaction confirmation
  const receipt = await publicClient.waitForTransactionReceipt({
    hash,
    confirmations: 1
  });
  
  if (receipt.status !== 'success') {
    throw new Error('Transaction failed');
  }
  
  return {
    transactionHash: hash,
    distributionAmount: faucetInfo.distributionAmount.toString()
  };
}
