/**
 * Multicall Utilities for Batch Contract Queries
 * 
 * This module provides utilities for using the JovayMulticall contract
 * to perform batch operations, significantly improving performance when
 * making multiple contract calls.
 * 
 * Key benefits:
 * - Reduces network round trips from N calls to 1 call
 * - Lower gas costs for read operations
 * - Better user experience with faster loading times
 * - Atomic operations - all calls succeed or fail together
 */

import { createPublicClient, http, encodeFunctionData, decodeFunctionResult } from 'viem';
import { jovayTestnet, erc20Abi, multicallContractAddress, multicallAbi } from './config.js';

// Create public client for multicall operations
function createClient() {
  return createPublicClient({
    chain: jovayTestnet,
    transport: http()
  });
}

/**
 * Batch fetch ERC20 token information (name, symbol, decimals) for multiple tokens
 * @param {string[]} tokenAddresses - Array of token contract addresses
 * @returns {Promise<Array>} Array of token info objects
 */
export async function batchGetTokenInfo(tokenAddresses) {
  if (!tokenAddresses || tokenAddresses.length === 0) {
    return [];
  }

  const publicClient = createClient();

  try {
    // Prepare multicall data
    const targets = [];
    const callData = [];
    
    // For each token, prepare calls for name, symbol, and decimals
    tokenAddresses.forEach(tokenAddress => {
      // name() call
      targets.push(tokenAddress);
      callData.push(encodeFunctionData({
        abi: erc20Abi,
        functionName: 'name'
      }));
      
      // symbol() call
      targets.push(tokenAddress);
      callData.push(encodeFunctionData({
        abi: erc20Abi,
        functionName: 'symbol'
      }));
      
      // decimals() call
      targets.push(tokenAddress);
      callData.push(encodeFunctionData({
        abi: erc20Abi,
        functionName: 'decimals'
      }));
    });

    // Execute multicall
    const results = await publicClient.readContract({
      address: multicallContractAddress,
      abi: multicallAbi,
      functionName: 'multiStaticCall',
      args: [targets, callData]
    });

    // Process results
    const tokenInfos = [];
    for (let i = 0; i < tokenAddresses.length; i++) {
      const tokenAddress = tokenAddresses[i];
      const baseIndex = i * 3;
      
      try {
        // Decode results for this token
        const name = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'name',
          data: results[baseIndex]
        });
        
        const symbol = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'symbol',
          data: results[baseIndex + 1]
        });
        
        const decimals = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'decimals',
          data: results[baseIndex + 2]
        });

        tokenInfos.push({
          address: tokenAddress,
          name: name || 'Unknown Token',
          symbol: symbol || 'UNKNOWN',
          decimals: Number(decimals) || 18
        });
      } catch (decodeError) {
        console.warn(`Failed to decode token info for ${tokenAddress}:`, decodeError);
        
        // Add fallback data for failed tokens
        tokenInfos.push({
          address: tokenAddress,
          name: 'Unknown Token',
          symbol: 'UNKNOWN',
          decimals: 18
        });
      }
    }

    return tokenInfos;
  } catch (error) {
    console.error('Multicall failed:', error);
    throw error;
  }
}

/**
 * Batch fetch only token names for multiple tokens
 * @param {string[]} tokenAddresses - Array of token contract addresses
 * @returns {Promise<Array>} Array of token names
 */
export async function batchGetTokenNames(tokenAddresses) {
  if (!tokenAddresses || tokenAddresses.length === 0) {
    return [];
  }

  const publicClient = createClient();

  try {
    // Prepare multicall data for names only
    const targets = tokenAddresses;
    const callData = tokenAddresses.map(() => 
      encodeFunctionData({
        abi: erc20Abi,
        functionName: 'name'
      })
    );

    // Execute multicall
    const results = await publicClient.readContract({
      address: multicallContractAddress,
      abi: multicallAbi,
      functionName: 'multiStaticCall',
      args: [targets, callData]
    });

    // Decode results
    return results.map((result, index) => {
      try {
        const name = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'name',
          data: result
        });
        return name || 'Unknown Token';
      } catch (error) {
        console.warn(`Failed to decode token name for ${tokenAddresses[index]}:`, error);
        return 'Unknown Token';
      }
    });
  } catch (error) {
    console.error('Batch get token names failed:', error);
    throw error;
  }
}

/**
 * Batch fetch only token symbols for multiple tokens
 * @param {string[]} tokenAddresses - Array of token contract addresses
 * @returns {Promise<Array>} Array of token symbols
 */
export async function batchGetTokenSymbols(tokenAddresses) {
  if (!tokenAddresses || tokenAddresses.length === 0) {
    return [];
  }

  const publicClient = createClient();

  try {
    // Prepare multicall data for symbols only
    const targets = tokenAddresses;
    const callData = tokenAddresses.map(() => 
      encodeFunctionData({
        abi: erc20Abi,
        functionName: 'symbol'
      })
    );

    // Execute multicall
    const results = await publicClient.readContract({
      address: multicallContractAddress,
      abi: multicallAbi,
      functionName: 'multiStaticCall',
      args: [targets, callData]
    });

    // Decode results
    return results.map((result, index) => {
      try {
        const symbol = decodeFunctionResult({
          abi: erc20Abi,
          functionName: 'symbol',
          data: result
        });
        return symbol || 'UNKNOWN';
      } catch (error) {
        console.warn(`Failed to decode token symbol for ${tokenAddresses[index]}:`, error);
        return 'UNKNOWN';
      }
    });
  } catch (error) {
    console.error('Batch get token symbols failed:', error);
    throw error;
  }
}

/**
 * Generic multicall function for custom contract calls
 * @param {Array} calls - Array of call objects: { target, abi, functionName, args? }
 * @returns {Promise<Array>} Array of decoded results
 */
export async function genericMulticall(calls) {
  if (!calls || calls.length === 0) {
    return [];
  }

  const publicClient = createClient();

  try {
    // Prepare multicall data
    const targets = calls.map(call => call.target);
    const callData = calls.map(call => 
      encodeFunctionData({
        abi: call.abi,
        functionName: call.functionName,
        args: call.args || []
      })
    );

    // Execute multicall
    const results = await publicClient.readContract({
      address: multicallContractAddress,
      abi: multicallAbi,
      functionName: 'multiStaticCall',
      args: [targets, callData]
    });

    // Decode results
    return results.map((result, index) => {
      try {
        return decodeFunctionResult({
          abi: calls[index].abi,
          functionName: calls[index].functionName,
          data: result
        });
      } catch (error) {
        console.warn(`Failed to decode result for call ${index}:`, error);
        return null;
      }
    });
  } catch (error) {
    console.error('Generic multicall failed:', error);
    throw error;
  }
}

/**
 * Check if multicall contract is available
 * @returns {Promise<boolean>} True if multicall contract is deployed and working
 */
export async function isMulticallAvailable() {
  try {
    const publicClient = createClient();
    
    // Try a simple multicall with no operations
    await publicClient.readContract({
      address: multicallContractAddress,
      abi: multicallAbi,
      functionName: 'multiStaticCall',
      args: [[], []]
    });
    
    return true;
  } catch (error) {
    console.warn('Multicall contract not available:', error);
    return false;
  }
}
